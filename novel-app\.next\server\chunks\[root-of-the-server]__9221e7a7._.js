module.exports = [
"[project]/.next-internal/server/app/api/rewrite/retry/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/fs [external] (fs, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}),
"[externals]/path [external] (path, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}),
"[externals]/crypto [external] (crypto, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}),
"[project]/src/lib/database.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "chapterContextDb",
    ()=>chapterContextDb,
    "chapterDb",
    ()=>chapterDb,
    "characterDb",
    ()=>characterDb,
    "jobDb",
    ()=>jobDb,
    "novelContextDb",
    ()=>novelContextDb,
    "novelDb",
    ()=>novelDb,
    "presetDb",
    ()=>presetDb,
    "ruleDb",
    ()=>ruleDb
]);
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/crypto [external] (crypto, cjs)");
;
;
;
// 数据存储路径
const DATA_DIR = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'data');
const NOVELS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'novels.json');
const CHAPTERS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'chapters.json');
const RULES_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'rewrite_rules.json');
const JOBS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'rewrite_jobs.json');
const CHARACTERS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'characters.json');
const PRESETS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'presets.json');
const NOVEL_CONTEXTS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'novel-contexts.json');
const CHAPTER_CONTEXTS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'chapter-contexts.json');
// 确保数据目录存在
function ensureDataDir() {
    if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(DATA_DIR)) {
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].mkdirSync(DATA_DIR, {
            recursive: true
        });
    }
}
// 读取JSON文件
function readJsonFile(filePath) {
    ensureDataDir();
    if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(filePath)) {
        return [];
    }
    try {
        const data = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(filePath, 'utf-8');
        return JSON.parse(data);
    } catch (error) {
        console.error(`Error reading ${filePath}:`, error);
        return [];
    }
}
// 写入JSON文件
function writeJsonFile(filePath, data) {
    ensureDataDir();
    try {
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf-8');
    } catch (error) {
        console.error(`Error writing ${filePath}:`, error);
        throw error;
    }
}
// 生成唯一ID
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
}
// 基于内容生成确定性ID
function generateDeterministicId(content) {
    return __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].createHash('md5').update(content).digest('hex').substring(0, 18);
}
const novelDb = {
    getAll: ()=>readJsonFile(NOVELS_FILE),
    getById: (id)=>{
        const novels = readJsonFile(NOVELS_FILE);
        return novels.find((novel)=>novel.id === id);
    },
    create: (novel)=>{
        const novels = readJsonFile(NOVELS_FILE);
        // 使用书名生成确定性ID
        const novelId = generateDeterministicId(novel.title);
        // 检查是否已存在相同ID的小说
        const existingNovel = novels.find((n)=>n.id === novelId);
        if (existingNovel) {
            // 如果已存在，更新现有记录
            existingNovel.filename = novel.filename;
            existingNovel.chapterCount = novel.chapterCount;
            writeJsonFile(NOVELS_FILE, novels);
            return existingNovel;
        }
        const newNovel = {
            ...novel,
            id: novelId,
            createdAt: new Date().toISOString()
        };
        novels.push(newNovel);
        writeJsonFile(NOVELS_FILE, novels);
        return newNovel;
    },
    update: (id, updates)=>{
        const novels = readJsonFile(NOVELS_FILE);
        const index = novels.findIndex((novel)=>novel.id === id);
        if (index === -1) return null;
        novels[index] = {
            ...novels[index],
            ...updates
        };
        writeJsonFile(NOVELS_FILE, novels);
        return novels[index];
    },
    delete: (id)=>{
        const novels = readJsonFile(NOVELS_FILE);
        const index = novels.findIndex((novel)=>novel.id === id);
        if (index === -1) return false;
        novels.splice(index, 1);
        writeJsonFile(NOVELS_FILE, novels);
        return true;
    }
};
const chapterDb = {
    getAll: ()=>readJsonFile(CHAPTERS_FILE),
    getByNovelId: (novelId)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        return chapters.filter((chapter)=>chapter.novelId === novelId);
    },
    getById: (id)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        return chapters.find((chapter)=>chapter.id === id);
    },
    create: (chapter)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        const newChapter = {
            ...chapter,
            id: generateId(),
            createdAt: new Date().toISOString()
        };
        chapters.push(newChapter);
        writeJsonFile(CHAPTERS_FILE, chapters);
        return newChapter;
    },
    createBatch: (chapters)=>{
        const existingChapters = readJsonFile(CHAPTERS_FILE);
        const newChapters = chapters.map((chapter)=>({
                ...chapter,
                id: generateId(),
                createdAt: new Date().toISOString()
            }));
        existingChapters.push(...newChapters);
        writeJsonFile(CHAPTERS_FILE, existingChapters);
        return newChapters;
    },
    delete: (id)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        const index = chapters.findIndex((chapter)=>chapter.id === id);
        if (index === -1) return false;
        chapters.splice(index, 1);
        writeJsonFile(CHAPTERS_FILE, chapters);
        return true;
    },
    deleteByNovelId: (novelId)=>{
        const chapters = readJsonFile(CHAPTERS_FILE);
        const filteredChapters = chapters.filter((chapter)=>chapter.novelId !== novelId);
        writeJsonFile(CHAPTERS_FILE, filteredChapters);
        return true;
    }
};
const ruleDb = {
    getAll: ()=>readJsonFile(RULES_FILE),
    getById: (id)=>{
        const rules = readJsonFile(RULES_FILE);
        return rules.find((rule)=>rule.id === id);
    },
    create: (rule)=>{
        const rules = readJsonFile(RULES_FILE);
        const newRule = {
            ...rule,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        rules.push(newRule);
        writeJsonFile(RULES_FILE, rules);
        return newRule;
    },
    update: (id, updates)=>{
        const rules = readJsonFile(RULES_FILE);
        const index = rules.findIndex((rule)=>rule.id === id);
        if (index === -1) return null;
        rules[index] = {
            ...rules[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        writeJsonFile(RULES_FILE, rules);
        return rules[index];
    },
    delete: (id)=>{
        const rules = readJsonFile(RULES_FILE);
        const index = rules.findIndex((rule)=>rule.id === id);
        if (index === -1) return false;
        rules.splice(index, 1);
        writeJsonFile(RULES_FILE, rules);
        return true;
    }
};
const jobDb = {
    getAll: ()=>readJsonFile(JOBS_FILE),
    getById: (id)=>{
        const jobs = readJsonFile(JOBS_FILE);
        return jobs.find((job)=>job.id === id);
    },
    create: (job)=>{
        const jobs = readJsonFile(JOBS_FILE);
        const newJob = {
            ...job,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        jobs.push(newJob);
        writeJsonFile(JOBS_FILE, jobs);
        return newJob;
    },
    update: (id, updates)=>{
        const jobs = readJsonFile(JOBS_FILE);
        const index = jobs.findIndex((job)=>job.id === id);
        if (index === -1) return null;
        jobs[index] = {
            ...jobs[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        writeJsonFile(JOBS_FILE, jobs);
        return jobs[index];
    },
    delete: (id)=>{
        const jobs = readJsonFile(JOBS_FILE);
        const index = jobs.findIndex((job)=>job.id === id);
        if (index === -1) return false;
        jobs.splice(index, 1);
        writeJsonFile(JOBS_FILE, jobs);
        return true;
    }
};
const characterDb = {
    getAll: ()=>readJsonFile(CHARACTERS_FILE),
    getByNovelId: (novelId)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        return characters.filter((character)=>character.novelId === novelId);
    },
    getById: (id)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        return characters.find((character)=>character.id === id);
    },
    create: (character)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        const newCharacter = {
            ...character,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        characters.push(newCharacter);
        writeJsonFile(CHARACTERS_FILE, characters);
        return newCharacter;
    },
    update: (id, updates)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        const index = characters.findIndex((character)=>character.id === id);
        if (index === -1) return null;
        characters[index] = {
            ...characters[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        writeJsonFile(CHARACTERS_FILE, characters);
        return characters[index];
    },
    delete: (id)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        const index = characters.findIndex((character)=>character.id === id);
        if (index === -1) return false;
        characters.splice(index, 1);
        writeJsonFile(CHARACTERS_FILE, characters);
        return true;
    },
    deleteByNovelId: (novelId)=>{
        const characters = readJsonFile(CHARACTERS_FILE);
        const filteredCharacters = characters.filter((character)=>character.novelId !== novelId);
        writeJsonFile(CHARACTERS_FILE, filteredCharacters);
        return true;
    }
};
const presetDb = {
    getAll: ()=>readJsonFile(PRESETS_FILE),
    getById: (id)=>{
        const presets = readJsonFile(PRESETS_FILE);
        return presets.find((preset)=>preset.id === id);
    },
    create: (preset)=>{
        const presets = readJsonFile(PRESETS_FILE);
        const newPreset = {
            ...preset,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        presets.push(newPreset);
        writeJsonFile(PRESETS_FILE, presets);
        return newPreset;
    },
    update: (id, updates)=>{
        const presets = readJsonFile(PRESETS_FILE);
        const index = presets.findIndex((preset)=>preset.id === id);
        if (index === -1) return null;
        presets[index] = {
            ...presets[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        writeJsonFile(PRESETS_FILE, presets);
        return presets[index];
    },
    delete: (id)=>{
        const presets = readJsonFile(PRESETS_FILE);
        const index = presets.findIndex((preset)=>preset.id === id);
        if (index === -1) return false;
        presets.splice(index, 1);
        writeJsonFile(PRESETS_FILE, presets);
        return true;
    }
};
const novelContextDb = {
    getAll: ()=>readJsonFile(NOVEL_CONTEXTS_FILE),
    getByNovelId: (novelId)=>{
        const contexts = readJsonFile(NOVEL_CONTEXTS_FILE);
        return contexts.find((context)=>context.novelId === novelId);
    },
    getById: (id)=>{
        const contexts = readJsonFile(NOVEL_CONTEXTS_FILE);
        return contexts.find((context)=>context.id === id);
    },
    create: (context)=>{
        const contexts = readJsonFile(NOVEL_CONTEXTS_FILE);
        const newContext = {
            ...context,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        contexts.push(newContext);
        writeJsonFile(NOVEL_CONTEXTS_FILE, contexts);
        return newContext;
    },
    update: (id, updates)=>{
        const contexts = readJsonFile(NOVEL_CONTEXTS_FILE);
        const index = contexts.findIndex((context)=>context.id === id);
        if (index === -1) return undefined;
        contexts[index] = {
            ...contexts[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        writeJsonFile(NOVEL_CONTEXTS_FILE, contexts);
        return contexts[index];
    },
    delete: (id)=>{
        const contexts = readJsonFile(NOVEL_CONTEXTS_FILE);
        const index = contexts.findIndex((context)=>context.id === id);
        if (index === -1) return false;
        contexts.splice(index, 1);
        writeJsonFile(NOVEL_CONTEXTS_FILE, contexts);
        return true;
    }
};
const chapterContextDb = {
    getAll: ()=>readJsonFile(CHAPTER_CONTEXTS_FILE),
    getByNovelId: (novelId)=>{
        const contexts = readJsonFile(CHAPTER_CONTEXTS_FILE);
        return contexts.filter((context)=>context.novelId === novelId);
    },
    getByChapter: (novelId, chapterNumber)=>{
        const contexts = readJsonFile(CHAPTER_CONTEXTS_FILE);
        return contexts.find((context)=>context.novelId === novelId && context.chapterNumber === chapterNumber);
    },
    getById: (id)=>{
        const contexts = readJsonFile(CHAPTER_CONTEXTS_FILE);
        return contexts.find((context)=>context.id === id);
    },
    create: (context)=>{
        const contexts = readJsonFile(CHAPTER_CONTEXTS_FILE);
        const newContext = {
            ...context,
            id: generateId(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        contexts.push(newContext);
        writeJsonFile(CHAPTER_CONTEXTS_FILE, contexts);
        return newContext;
    },
    update: (id, updates)=>{
        const contexts = readJsonFile(CHAPTER_CONTEXTS_FILE);
        const index = contexts.findIndex((context)=>context.id === id);
        if (index === -1) return undefined;
        contexts[index] = {
            ...contexts[index],
            ...updates,
            updatedAt: new Date().toISOString()
        };
        writeJsonFile(CHAPTER_CONTEXTS_FILE, contexts);
        return contexts[index];
    },
    delete: (id)=>{
        const contexts = readJsonFile(CHAPTER_CONTEXTS_FILE);
        const index = contexts.findIndex((context)=>context.id === id);
        if (index === -1) return false;
        contexts.splice(index, 1);
        writeJsonFile(CHAPTER_CONTEXTS_FILE, contexts);
        return true;
    },
    // 获取章节的上下文窗口（前后几章的上下文）
    getContextWindow: (novelId, chapterNumber, windowSize = 2)=>{
        const contexts = readJsonFile(CHAPTER_CONTEXTS_FILE);
        const novelContexts = contexts.filter((context)=>context.novelId === novelId);
        const startChapter = Math.max(1, chapterNumber - windowSize);
        const endChapter = chapterNumber + windowSize;
        return novelContexts.filter((context)=>context.chapterNumber >= startChapter && context.chapterNumber <= endChapter).sort((a, b)=>a.chapterNumber - b.chapterNumber);
    }
};
}),
"[project]/src/lib/context-client.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

// 客户端上下文工具函数
__turbopack_context__.s([
    "analyzeNovelClient",
    ()=>analyzeNovelClient,
    "getChapterContext",
    ()=>getChapterContext,
    "getChapterContextClient",
    ()=>getChapterContextClient,
    "getChapterContextWindow",
    ()=>getChapterContextWindow,
    "getChapterContextWindowClient",
    ()=>getChapterContextWindowClient,
    "getNovelContext",
    ()=>getNovelContext,
    "getNovelContextClient",
    ()=>getNovelContextClient,
    "rewriteTextWithContextClient",
    ()=>rewriteTextWithContextClient
]);
// 检查是否在客户端环境
function isClientSide() {
    return "undefined" !== 'undefined';
}
async function getNovelContextClient(novelId) {
    if (!isClientSide()) {
        throw new Error('This function can only be used on client side');
    }
    try {
        const response = await fetch(`/api/context/novel?novelId=${novelId}`);
        const result = await response.json();
        if (!result.success) {
            console.warn('获取小说上下文失败:', result.error);
            return null;
        }
        return result.data;
    } catch (error) {
        console.error('获取小说上下文失败:', error);
        return null;
    }
}
async function getChapterContextClient(novelId, chapterNumber) {
    if (!isClientSide()) {
        throw new Error('This function can only be used on client side');
    }
    try {
        const response = await fetch(`/api/context/chapter?novelId=${novelId}&chapterNumber=${chapterNumber}`);
        const result = await response.json();
        if (!result.success) {
            console.warn('获取章节上下文失败:', result.error);
            return null;
        }
        return result.data;
    } catch (error) {
        console.error('获取章节上下文失败:', error);
        return null;
    }
}
async function getChapterContextWindowClient(novelId, chapterNumber, windowSize = 2) {
    if (!isClientSide()) {
        throw new Error('This function can only be used on client side');
    }
    try {
        const response = await fetch(`/api/context/window?novelId=${novelId}&chapterNumber=${chapterNumber}&windowSize=${windowSize}`);
        const result = await response.json();
        if (!result.success) {
            console.warn('获取章节上下文窗口失败:', result.error);
            return [];
        }
        return result.data.contexts;
    } catch (error) {
        console.error('获取章节上下文窗口失败:', error);
        return [];
    }
}
async function analyzeNovelClient(novelId, analyzeChapters = false) {
    if (!isClientSide()) {
        throw new Error('This function can only be used on client side');
    }
    const response = await fetch('/api/context/analyze', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            novelId: novelId,
            analyzeChapters: analyzeChapters
        })
    });
    const result = await response.json();
    if (!result.success) {
        throw new Error(result.error || '分析失败');
    }
    return {
        novelContext: result.data.novelContext,
        chapterContexts: result.data.chapterContexts
    };
}
async function rewriteTextWithContextClient(novelId, chapterNumbers, rules, model, onProgress) {
    if (!isClientSide()) {
        throw new Error('This function can only be used on client side');
    }
    const response = await fetch('/api/rewrite', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            novelId: novelId,
            chapterNumbers: chapterNumbers,
            rules: rules,
            model: model || 'gemini-2.5-flash-lite'
        })
    });
    const result = await response.json();
    if (!result.success) {
        throw new Error(result.error || '重写失败');
    }
    return result.data.results;
}
async function getNovelContext(novelId) {
    if (isClientSide()) //TURBOPACK unreachable
    ;
    else {
        // 服务端环境，动态导入服务端函数
        const { getNovelContextServer } = await __turbopack_context__.A("[project]/src/lib/context-utils.ts [app-route] (ecmascript, async loader)");
        return getNovelContextServer(novelId);
    }
}
async function getChapterContext(novelId, chapterNumber) {
    if (isClientSide()) //TURBOPACK unreachable
    ;
    else {
        // 服务端环境，动态导入服务端函数
        const { getChapterContextServer } = await __turbopack_context__.A("[project]/src/lib/context-utils.ts [app-route] (ecmascript, async loader)");
        return getChapterContextServer(novelId, chapterNumber);
    }
}
async function getChapterContextWindow(novelId, chapterNumber, windowSize = 2) {
    if (isClientSide()) //TURBOPACK unreachable
    ;
    else {
        // 服务端环境，动态导入服务端函数
        const { getChapterContextWindowServer } = await __turbopack_context__.A("[project]/src/lib/context-utils.ts [app-route] (ecmascript, async loader)");
        return getChapterContextWindowServer(novelId, chapterNumber, windowSize);
    }
}
}),
"[project]/src/lib/context-utils.ts [app-route] (ecmascript) <locals>", ((__turbopack_context__) => {
"use strict";

// 上下文工具函数 - 服务端和客户端通用
__turbopack_context__.s([
    "getChapterContextServer",
    ()=>getChapterContextServer,
    "getChapterContextWindowServer",
    ()=>getChapterContextWindowServer,
    "getNovelContextServer",
    ()=>getNovelContextServer,
    "rewriteChaptersWithContext",
    ()=>rewriteChaptersWithContext,
    "rewriteTextWithContextServer",
    ()=>rewriteTextWithContextServer
]);
// 重新导出客户端函数，提供统一接口
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/context-client.ts [app-route] (ecmascript)");
// 检查是否在服务端环境
function isServerSide() {
    return "undefined" === 'undefined';
}
async function rewriteTextWithContextServer(novelId, chapterNumber, originalText, rules, chapterTitle, model) {
    if (!isServerSide()) //TURBOPACK unreachable
    ;
    try {
        // 导入服务端模块
        const { rewriteText } = await __turbopack_context__.A("[project]/src/lib/gemini.ts [app-route] (ecmascript, async loader)");
        const { novelContextDb, chapterContextDb } = await __turbopack_context__.A("[project]/src/lib/database.ts [app-route] (ecmascript, async loader)");
        // 获取小说整体上下文
        const novelContext = novelContextDb.getByNovelId(novelId);
        // 获取章节上下文
        const chapterContext1 = chapterContextDb.getByChapter(novelId, chapterNumber);
        // 构建请求
        const request = {
            originalText,
            rules,
            chapterTitle,
            chapterNumber,
            model,
            novelContext: novelContext ? {
                summary: novelContext.summary,
                mainCharacters: novelContext.mainCharacters,
                worldSetting: novelContext.worldSetting,
                writingStyle: novelContext.writingStyle,
                tone: novelContext.tone
            } : undefined,
            chapterContext: chapterContext1 ? {
                previousChapterSummary: chapterContext1.previousChapterSummary,
                keyEvents: chapterContext1.keyEvents,
                characterStates: chapterContext1.characterStates,
                plotProgress: chapterContext1.plotProgress,
                contextualNotes: chapterContext1.contextualNotes
            } : undefined
        };
        return await rewriteText(request);
    } catch (error) {
        console.error('带上下文重写失败:', error);
        // 如果获取上下文失败，回退到普通重写
        const { rewriteText } = await __turbopack_context__.A("[project]/src/lib/gemini.ts [app-route] (ecmascript, async loader)");
        return await rewriteText({
            originalText,
            rules,
            chapterTitle,
            chapterNumber,
            model
        });
    }
}
async function getNovelContextServer(novelId) {
    if (!isServerSide()) //TURBOPACK unreachable
    ;
    const { novelContextDb } = await __turbopack_context__.A("[project]/src/lib/database.ts [app-route] (ecmascript, async loader)");
    return novelContextDb.getByNovelId(novelId);
}
async function getChapterContextServer(novelId, chapterNumber) {
    if (!isServerSide()) //TURBOPACK unreachable
    ;
    const { chapterContextDb } = await __turbopack_context__.A("[project]/src/lib/database.ts [app-route] (ecmascript, async loader)");
    return chapterContextDb.getByChapter(novelId, chapterNumber);
}
async function getChapterContextWindowServer(novelId, chapterNumber, windowSize = 2) {
    if (!isServerSide()) //TURBOPACK unreachable
    ;
    const { chapterContextDb } = await __turbopack_context__.A("[project]/src/lib/database.ts [app-route] (ecmascript, async loader)");
    return chapterContextDb.getContextWindow(novelId, chapterNumber, windowSize);
}
async function rewriteChaptersWithContext(novelId, chapters, rules, onProgress, onChapterComplete, concurrency = 3, model = 'gemini-2.5-flash-lite', enableFailureRecovery = true) {
    if (!isServerSide()) //TURBOPACK unreachable
    ;
    try {
        // 导入所需模块
        const { novelContextDb, chapterContextDb } = await __turbopack_context__.A("[project]/src/lib/database.ts [app-route] (ecmascript, async loader)");
        const { rewriteText } = await __turbopack_context__.A("[project]/src/lib/gemini.ts [app-route] (ecmascript, async loader)");
        // 获取小说整体上下文
        const novelContext = novelContextDb.getByNovelId(novelId);
        const results = new Array(chapters.length);
        let completed = 0;
        let totalTokensUsed = 0;
        const startTime = Date.now();
        // 使用信号量控制并发
        const semaphore = {
            count: concurrency,
            waiting: []
        };
        const acquire = ()=>new Promise((resolve)=>{
                if (semaphore.count > 0) {
                    semaphore.count--;
                    resolve();
                } else {
                    semaphore.waiting.push(resolve);
                }
            });
        const release = ()=>{
            semaphore.count++;
            if (semaphore.waiting.length > 0) {
                const next = semaphore.waiting.shift();
                if (next) {
                    semaphore.count--;
                    next();
                }
            }
        };
        const processChapter = async (chapter, index)=>{
            await acquire();
            const chapterStartTime = Date.now();
            try {
                // 获取章节上下文
                const chapterContext1 = chapterContextDb.getByChapter(novelId, chapter.number);
                // 构建带上下文的请求
                const request = {
                    originalText: chapter.content,
                    rules,
                    chapterTitle: chapter.title,
                    chapterNumber: chapter.number,
                    model,
                    novelContext: novelContext ? {
                        summary: novelContext.summary,
                        mainCharacters: novelContext.mainCharacters,
                        worldSetting: novelContext.worldSetting,
                        writingStyle: novelContext.writingStyle,
                        tone: novelContext.tone
                    } : undefined,
                    chapterContext: chapterContext1 ? {
                        previousChapterSummary: chapterContext1.previousChapterSummary,
                        keyEvents: chapterContext1.keyEvents,
                        characterStates: chapterContext1.characterStates,
                        plotProgress: chapterContext1.plotProgress,
                        contextualNotes: chapterContext1.contextualNotes
                    } : undefined
                };
                const result = await rewriteText(request);
                const chapterProcessingTime = Date.now() - chapterStartTime;
                if (result.tokensUsed) {
                    totalTokensUsed += result.tokensUsed;
                }
                const chapterResult = {
                    success: result.success,
                    content: result.rewrittenText,
                    error: result.error,
                    details: {
                        apiKeyUsed: result.apiKeyUsed,
                        tokensUsed: result.tokensUsed,
                        model: result.model,
                        processingTime: chapterProcessingTime,
                        chapterNumber: chapter.number,
                        chapterTitle: chapter.title,
                        hasContext: !!(novelContext || chapterContext1),
                        detailedError: result.detailedError,
                        debugInfo: result.debugInfo
                    }
                };
                results[index] = chapterResult;
                completed++;
                // 实时回调章节完成
                if (onChapterComplete) {
                    onChapterComplete(index, chapterResult);
                }
                // 更新进度
                const progress = completed / chapters.length * 100;
                const totalTime = Date.now() - startTime;
                const averageTimePerChapter = totalTime / completed;
                if (onProgress) {
                    onProgress(progress, chapter.number, {
                        completed,
                        totalTokensUsed,
                        totalTime,
                        averageTimePerChapter,
                        hasContext: !!(novelContext || chapterContext1)
                    });
                }
                console.log(`第 ${chapter.number} 章重写${result.success ? '成功' : '失败'}${novelContext || chapterContext1 ? '（使用上下文）' : ''}: ${result.error || '完成'}`);
            } catch (error) {
                const chapterProcessingTime = Date.now() - chapterStartTime;
                const chapterResult = {
                    success: false,
                    content: '',
                    error: `重写异常: ${error instanceof Error ? error.message : '未知错误'}`,
                    details: {
                        processingTime: chapterProcessingTime,
                        chapterNumber: chapter.number,
                        chapterTitle: chapter.title,
                        hasContext: !!(novelContext || chapterContext)
                    }
                };
                results[index] = chapterResult;
                completed++;
                if (onChapterComplete) {
                    onChapterComplete(index, chapterResult);
                }
                console.error(`第 ${chapter.number} 章重写异常:`, error);
            } finally{
                release();
            }
        };
        // 并行处理所有章节
        const promises = chapters.map((chapter, index)=>processChapter(chapter, index));
        await Promise.all(promises);
        return results;
    } catch (error) {
        console.error('批量重写失败，回退到普通重写:', error);
        // 如果上下文重写失败，回退到普通重写
        const { rewriteChapters } = await __turbopack_context__.A("[project]/src/lib/gemini.ts [app-route] (ecmascript, async loader)");
        return await rewriteChapters(chapters, rules, onProgress, onChapterComplete, concurrency, model, enableFailureRecovery);
    }
}
;
}),
"[project]/src/lib/file-manager.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "FileManager",
    ()=>FileManager,
    "fileManager",
    ()=>fileManager
]);
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
class FileManager {
    static instance;
    baseDir;
    constructor(){
        this.baseDir = process.cwd();
    }
    static getInstance() {
        if (!FileManager.instance) {
            FileManager.instance = new FileManager();
        }
        return FileManager.instance;
    }
    // 确保目录存在
    ensureDir(dirPath) {
        if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(dirPath)) {
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].mkdirSync(dirPath, {
                recursive: true
            });
        }
    }
    // 获取novels目录路径
    getNovelsDir() {
        return __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.baseDir, '..', 'novels');
    }
    // 获取chapters目录路径
    getChaptersDir() {
        return __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.baseDir, '..', 'chapters');
    }
    // 获取数据目录路径
    getDataDir() {
        const dataDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.baseDir, 'data');
        this.ensureDir(dataDir);
        return dataDir;
    }
    // 获取改写结果目录路径
    getRewrittenDir() {
        const rewrittenDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.getDataDir(), 'rewritten');
        this.ensureDir(rewrittenDir);
        return rewrittenDir;
    }
    // 获取特定小说的改写结果目录
    getNovelRewrittenDir(novelTitle) {
        const novelDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.getRewrittenDir(), this.sanitizeFilename(novelTitle));
        this.ensureDir(novelDir);
        return novelDir;
    }
    // 获取完成小说目录路径
    getDoneNovelsDir() {
        const doneNovelsDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.baseDir, '..', 'done-novels');
        this.ensureDir(doneNovelsDir);
        return doneNovelsDir;
    }
    // 获取特定小说的章节目录
    getNovelChaptersDir(novelTitle) {
        const chaptersDir = this.getChaptersDir();
        this.ensureDir(chaptersDir);
        const novelDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(chaptersDir, this.sanitizeFilename(novelTitle));
        this.ensureDir(novelDir);
        return novelDir;
    }
    // 清理文件名中的非法字符
    sanitizeFilename(filename) {
        return filename.replace(/[<>:"/\\|?*]/g, '_').trim();
    }
    // 读取文件内容
    readFile(filePath) {
        try {
            return __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readFileSync(filePath, 'utf-8');
        } catch (error) {
            console.error(`读取文件失败: ${filePath}`, error);
            throw error;
        }
    }
    // 写入文件内容
    writeFile(filePath, content) {
        try {
            const dir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(filePath);
            this.ensureDir(dir);
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(filePath, content, 'utf-8');
        } catch (error) {
            console.error(`写入文件失败: ${filePath}`, error);
            throw error;
        }
    }
    // 检查文件是否存在
    fileExists(filePath) {
        return __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(filePath);
    }
    // 检查目录是否存在
    directoryExists(dirPath) {
        try {
            return __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(dirPath) && __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].statSync(dirPath).isDirectory();
        } catch (error) {
            return false;
        }
    }
    // 获取目录中的所有文件
    listFiles(dirPath, extensions) {
        try {
            if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(dirPath)) {
                return [];
            }
            const files = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readdirSync(dirPath);
            if (extensions) {
                return files.filter((file)=>{
                    const ext = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].extname(file).toLowerCase();
                    return extensions.includes(ext);
                });
            }
            return files;
        } catch (error) {
            console.error(`读取目录失败: ${dirPath}`, error);
            return [];
        }
    }
    // 获取文件信息
    getFileStats(filePath) {
        try {
            return __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].statSync(filePath);
        } catch (error) {
            console.error(`获取文件信息失败: ${filePath}`, error);
            return null;
        }
    }
    // 删除文件
    deleteFile(filePath) {
        try {
            if (__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(filePath)) {
                __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].unlinkSync(filePath);
                return true;
            }
            return false;
        } catch (error) {
            console.error(`删除文件失败: ${filePath}`, error);
            return false;
        }
    }
    // 删除目录
    deleteDir(dirPath) {
        try {
            if (__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(dirPath)) {
                __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].rmSync(dirPath, {
                    recursive: true,
                    force: true
                });
                return true;
            }
            return false;
        } catch (error) {
            console.error(`删除目录失败: ${dirPath}`, error);
            return false;
        }
    }
    // 复制文件
    copyFile(srcPath, destPath) {
        try {
            const destDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(destPath);
            this.ensureDir(destDir);
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].copyFileSync(srcPath, destPath);
            return true;
        } catch (error) {
            console.error(`复制文件失败: ${srcPath} -> ${destPath}`, error);
            return false;
        }
    }
    // 移动文件
    moveFile(srcPath, destPath) {
        try {
            const destDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(destPath);
            this.ensureDir(destDir);
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].renameSync(srcPath, destPath);
            return true;
        } catch (error) {
            console.error(`移动文件失败: ${srcPath} -> ${destPath}`, error);
            return false;
        }
    }
    // 获取目录大小
    getDirSize(dirPath) {
        let totalSize = 0;
        try {
            if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(dirPath)) {
                return 0;
            }
            const files = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].readdirSync(dirPath);
            for (const file of files){
                const filePath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(dirPath, file);
                const stats = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].statSync(filePath);
                if (stats.isDirectory()) {
                    totalSize += this.getDirSize(filePath);
                } else {
                    totalSize += stats.size;
                }
            }
        } catch (error) {
            console.error(`计算目录大小失败: ${dirPath}`, error);
        }
        return totalSize;
    }
    // 格式化文件大小
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = [
            'B',
            'KB',
            'MB',
            'GB'
        ];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    // 创建备份
    createBackup(filePath) {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const ext = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].extname(filePath);
            const baseName = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].basename(filePath, ext);
            const dir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(filePath);
            const backupPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(dir, `${baseName}_backup_${timestamp}${ext}`);
            if (this.copyFile(filePath, backupPath)) {
                return backupPath;
            }
            return null;
        } catch (error) {
            console.error(`创建备份失败: ${filePath}`, error);
            return null;
        }
    }
    // 合并改写的章节为一个完整的小说文件
    mergeRewrittenChapters(novelTitle) {
        try {
            const rewrittenDir = this.getNovelRewrittenDir(novelTitle);
            const doneNovelsDir = this.getDoneNovelsDir();
            // 获取所有改写的章节文件
            const chapterFiles = this.listFiles(rewrittenDir, [
                '.txt'
            ]).filter((file)=>file.startsWith('chapter_') && file.includes('_rewritten')).sort((a, b)=>{
                // 提取章节号进行排序
                const aNum = parseInt(a.match(/chapter_(\d+)/)?.[1] || '0');
                const bNum = parseInt(b.match(/chapter_(\d+)/)?.[1] || '0');
                return aNum - bNum;
            });
            if (chapterFiles.length === 0) {
                return {
                    success: false,
                    error: '没有找到改写的章节文件'
                };
            }
            // 读取并合并所有章节
            let mergedContent = '';
            for (const chapterFile of chapterFiles){
                const chapterPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(rewrittenDir, chapterFile);
                const chapterContent = this.readFile(chapterPath);
                mergedContent += chapterContent + '\n\n';
            }
            // 生成合并后的文件名
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
            const mergedFileName = `${this.sanitizeFilename(novelTitle)}_merged_${timestamp}.txt`;
            const mergedFilePath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(doneNovelsDir, mergedFileName);
            // 写入合并后的文件
            this.writeFile(mergedFilePath, mergedContent.trim());
            return {
                success: true,
                filePath: mergedFilePath
            };
        } catch (error) {
            console.error(`合并章节失败: ${novelTitle}`, error);
            return {
                success: false,
                error: error instanceof Error ? error.message : '未知错误'
            };
        }
    }
    // 清理旧备份文件
    cleanupBackups(dirPath, maxBackups = 5) {
        try {
            const files = this.listFiles(dirPath);
            const backupFiles = files.filter((file)=>file.includes('_backup_')).map((file)=>({
                    name: file,
                    path: __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(dirPath, file),
                    stats: this.getFileStats(__TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(dirPath, file))
                })).filter((item)=>item.stats !== null).sort((a, b)=>b.stats.mtime.getTime() - a.stats.mtime.getTime());
            // 删除超出数量限制的备份文件
            if (backupFiles.length > maxBackups) {
                const filesToDelete = backupFiles.slice(maxBackups);
                for (const file of filesToDelete){
                    this.deleteFile(file.path);
                }
            }
        } catch (error) {
            console.error(`清理备份文件失败: ${dirPath}`, error);
        }
    }
}
const fileManager = FileManager.getInstance();
}),
"[project]/src/app/api/rewrite/retry/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "POST",
    ()=>POST
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/context-utils.ts [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/file-manager.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
;
;
;
async function POST(request) {
    try {
        const { jobId, chapterNumbers, rules, model } = await request.json();
        if (!jobId || !chapterNumbers || !Array.isArray(chapterNumbers) || !rules) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '参数不完整'
            }, {
                status: 400
            });
        }
        // 获取任务信息
        const job = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jobDb"].getById(jobId);
        if (!job) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '任务不存在'
            }, {
                status: 404
            });
        }
        // 获取小说信息
        const novel = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["novelDb"].getById(job.novelId);
        if (!novel) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '小说不存在'
            }, {
                status: 404
            });
        }
        // 获取要重试的章节
        const allChapters = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["chapterDb"].getByNovelId(job.novelId);
        const chaptersToRetry = allChapters.filter((chapter)=>chapterNumbers.includes(chapter.chapterNumber));
        if (chaptersToRetry.length === 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '没有找到指定的章节'
            }, {
                status: 404
            });
        }
        // 更新任务状态
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jobDb"].update(jobId, {
            status: 'processing',
            details: {
                ...job.details,
                retryInProgress: true,
                retryChapters: chapterNumbers
            }
        });
        // 异步执行重试
        executeRetryJob(jobId, chaptersToRetry, rules, novel.title, model || 'gemini-2.5-flash-lite');
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                jobId,
                retryChaptersCount: chaptersToRetry.length,
                message: '重试任务已创建，正在处理中...'
            }
        });
    } catch (error) {
        console.error('创建重试任务失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: '创建重试任务失败'
        }, {
            status: 500
        });
    }
}
// 异步执行重试任务
async function executeRetryJob(jobId, chapters, rules, novelTitle, model = 'gemini-2.5-flash-lite') {
    const startTime = Date.now();
    const outputDir = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileManager"].getNovelRewrittenDir(novelTitle);
    let successCount = 0;
    const retryResults = [];
    try {
        // 串行处理重试章节，避免API限制
        for (const chapter of chapters){
            console.log(`正在重试第 ${chapter.chapterNumber} 章: ${chapter.title}`);
            const chapterStartTime = Date.now();
            try {
                // 等待一段时间再处理下一个章节
                if (retryResults.length > 0) {
                    await new Promise((resolve)=>setTimeout(resolve, 3000));
                }
                const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$context$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["rewriteTextWithContextServer"])(chapter.novelId, chapter.chapterNumber, chapter.content, rules, chapter.title, model);
                const chapterProcessingTime = Date.now() - chapterStartTime;
                if (result.success) {
                    // 保存成功重试的章节
                    const filename = `chapter_${chapter.chapterNumber}_rewritten.txt`;
                    const filePath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(outputDir, filename);
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileManager"].writeFile(filePath, result.rewrittenText);
                    successCount++;
                }
                retryResults.push({
                    chapterNumber: chapter.chapterNumber,
                    chapterTitle: chapter.title,
                    success: result.success,
                    error: result.error,
                    apiKeyUsed: result.apiKeyUsed,
                    tokensUsed: result.tokensUsed,
                    processingTime: chapterProcessingTime
                });
                console.log(`第 ${chapter.chapterNumber} 章重试${result.success ? '成功' : '失败'}: ${result.error || '完成'}`);
            } catch (error) {
                const chapterProcessingTime = Date.now() - chapterStartTime;
                retryResults.push({
                    chapterNumber: chapter.chapterNumber,
                    chapterTitle: chapter.title,
                    success: false,
                    error: `重试异常: ${error instanceof Error ? error.message : '未知错误'}`,
                    processingTime: chapterProcessingTime
                });
                console.error(`第 ${chapter.chapterNumber} 章重试异常:`, error);
            }
        }
        // 更新任务状态
        const currentJob = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jobDb"].getById(jobId);
        if (currentJob?.details) {
            const updatedChapterResults = [
                ...currentJob.details.chapterResults || []
            ];
            // 更新重试章节的结果
            retryResults.forEach((retryResult)=>{
                const existingIndex = updatedChapterResults.findIndex((r)=>r && r.chapterNumber === retryResult.chapterNumber);
                if (existingIndex >= 0) {
                    updatedChapterResults[existingIndex] = {
                        ...updatedChapterResults[existingIndex],
                        ...retryResult,
                        completedAt: new Date().toISOString(),
                        isRetried: true
                    };
                }
            });
            const totalProcessingTime = Date.now() - startTime;
            const newCompletedCount = updatedChapterResults.filter((r)=>r && r.success).length;
            const newFailedCount = updatedChapterResults.filter((r)=>r && !r.success).length;
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jobDb"].update(jobId, {
                status: 'completed',
                progress: 100,
                result: `重试完成: ${successCount}/${chapters.length} 章节成功，总计 ${newCompletedCount}/${currentJob.details.totalChapters} 章节完成`,
                details: {
                    ...currentJob.details,
                    completedChapters: newCompletedCount,
                    failedChapters: newFailedCount,
                    chapterResults: updatedChapterResults,
                    retryInProgress: false,
                    retryResults,
                    lastRetryTime: new Date().toISOString(),
                    retryProcessingTime: totalProcessingTime
                }
            });
        }
        // 保存重试结果摘要
        const retrySummaryPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(outputDir, `retry_summary_${Date.now()}.json`);
        const retrySummaryData = JSON.stringify({
            jobId,
            novelTitle,
            retryChapters: chapters.map((c)=>({
                    number: c.chapterNumber,
                    title: c.title
                })),
            successCount,
            failedCount: chapters.length - successCount,
            totalProcessingTime: Date.now() - startTime,
            results: retryResults,
            completedAt: new Date().toISOString(),
            model
        }, null, 2);
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$file$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileManager"].writeFile(retrySummaryPath, retrySummaryData);
    } catch (error) {
        console.error('执行重试任务失败:', error);
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jobDb"].update(jobId, {
            status: 'failed',
            result: `重试失败: ${error instanceof Error ? error.message : '未知错误'}`,
            details: {
                ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jobDb"].getById(jobId)?.details,
                retryInProgress: false,
                retryError: error instanceof Error ? error.message : '未知错误'
            }
        });
    }
}
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__9221e7a7._.js.map