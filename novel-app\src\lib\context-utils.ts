// 上下文工具函数 - 服务端和客户端通用
import type { RewriteRequest, RewriteResponse } from './gemini';

// 检查是否在服务端环境
function isServerSide(): boolean {
  return typeof window === 'undefined';
}

// 带上下文的重写函数（服务端专用）
export async function rewriteTextWithContextServer(
  novelId: string,
  chapterNumber: number,
  originalText: string,
  rules: string,
  chapterTitle?: string,
  model?: string
): Promise<RewriteResponse> {
  if (!isServerSide()) {
    throw new Error('This function can only be used on server side');
  }

  try {
    // 导入服务端模块
    const { rewriteText } = await import('./gemini');
    const { novelContextDb, chapterContextDb } = await import('./database');

    // 获取小说整体上下文
    const novelContext = novelContextDb.getByNovelId(novelId);

    // 获取章节上下文
    const chapterContext = chapterContextDb.getByChapter(novelId, chapterNumber);

    // 构建请求
    const request: RewriteRequest = {
      originalText,
      rules,
      chapterTitle,
      chapterNumber,
      model,
      novelContext: novelContext ? {
        summary: novelContext.summary,
        mainCharacters: novelContext.mainCharacters,
        worldSetting: novelContext.worldSetting,
        writingStyle: novelContext.writingStyle,
        tone: novelContext.tone
      } : undefined,
      chapterContext: chapterContext ? {
        previousChapterSummary: chapterContext.previousChapterSummary,
        keyEvents: chapterContext.keyEvents,
        characterStates: chapterContext.characterStates,
        plotProgress: chapterContext.plotProgress,
        contextualNotes: chapterContext.contextualNotes
      } : undefined
    };

    return await rewriteText(request);
  } catch (error) {
    console.error('带上下文重写失败:', error);
    // 如果获取上下文失败，回退到普通重写
    const { rewriteText } = await import('./gemini');
    return await rewriteText({
      originalText,
      rules,
      chapterTitle,
      chapterNumber,
      model
    });
  }
}

// 获取小说上下文（服务端专用）
export async function getNovelContextServer(novelId: string) {
  if (!isServerSide()) {
    throw new Error('This function can only be used on server side');
  }

  const { novelContextDb } = await import('./database');
  return novelContextDb.getByNovelId(novelId);
}

// 获取章节上下文（服务端专用）
export async function getChapterContextServer(novelId: string, chapterNumber: number) {
  if (!isServerSide()) {
    throw new Error('This function can only be used on server side');
  }

  const { chapterContextDb } = await import('./database');
  return chapterContextDb.getByChapter(novelId, chapterNumber);
}

// 获取章节上下文窗口（服务端专用）
export async function getChapterContextWindowServer(
  novelId: string,
  chapterNumber: number,
  windowSize: number = 2
) {
  if (!isServerSide()) {
    throw new Error('This function can only be used on server side');
  }

  const { chapterContextDb } = await import('./database');
  return chapterContextDb.getContextWindow(novelId, chapterNumber, windowSize);
}

// 带上下文的批量重写函数（服务端专用）
export async function rewriteChaptersWithContext(
  novelId: string,
  chapters: Array<{ content: string; title: string; number: number }>,
  rules: string,
  onProgress?: (progress: number, currentChapter: number, details?: any) => void,
  onChapterComplete?: (chapterIndex: number, result: any) => void,
  concurrency: number = 3,
  model: string = 'gemini-flash-lite-latest',
  enableFailureRecovery: boolean = true
): Promise<Array<{ success: boolean; content: string; error?: string; details?: any }>> {
  if (!isServerSide()) {
    throw new Error('This function can only be used on server side');
  }

  try {
    // 导入所需模块
    const { novelContextDb, chapterContextDb } = await import('./database');
    const { rewriteText } = await import('./gemini');

    // 获取小说整体上下文
    const novelContext = novelContextDb.getByNovelId(novelId);

    const results: Array<{ success: boolean; content: string; error?: string; details?: any }> = new Array(chapters.length);
    let completed = 0;
    let totalTokensUsed = 0;
    const startTime = Date.now();

    // 使用信号量控制并发
    const semaphore = { count: concurrency, waiting: [] as Array<() => void> };

    const acquire = () => new Promise<void>(resolve => {
      if (semaphore.count > 0) {
        semaphore.count--;
        resolve();
      } else {
        semaphore.waiting.push(resolve);
      }
    });

    const release = () => {
      semaphore.count++;
      if (semaphore.waiting.length > 0) {
        const next = semaphore.waiting.shift();
        if (next) {
          semaphore.count--;
          next();
        }
      }
    };

    const processChapter = async (chapter: { content: string; title: string; number: number }, index: number) => {
      await acquire();
      const chapterStartTime = Date.now();

      try {
        // 获取章节上下文
        const chapterContext = chapterContextDb.getByChapter(novelId, chapter.number);

        // 构建带上下文的请求
        const request: RewriteRequest = {
          originalText: chapter.content,
          rules,
          chapterTitle: chapter.title,
          chapterNumber: chapter.number,
          model,
          novelContext: novelContext ? {
            summary: novelContext.summary,
            mainCharacters: novelContext.mainCharacters,
            worldSetting: novelContext.worldSetting,
            writingStyle: novelContext.writingStyle,
            tone: novelContext.tone
          } : undefined,
          chapterContext: chapterContext ? {
            previousChapterSummary: chapterContext.previousChapterSummary,
            keyEvents: chapterContext.keyEvents,
            characterStates: chapterContext.characterStates,
            plotProgress: chapterContext.plotProgress,
            contextualNotes: chapterContext.contextualNotes
          } : undefined
        };

        const result = await rewriteText(request);
        const chapterProcessingTime = Date.now() - chapterStartTime;

        if (result.tokensUsed) {
          totalTokensUsed += result.tokensUsed;
        }

        const chapterResult = {
          success: result.success,
          content: result.rewrittenText,
          error: result.error,
          details: {
            apiKeyUsed: result.apiKeyUsed,
            tokensUsed: result.tokensUsed,
            model: result.model,
            processingTime: chapterProcessingTime,
            chapterNumber: chapter.number,
            chapterTitle: chapter.title,
            hasContext: !!(novelContext || chapterContext),
            detailedError: result.detailedError, // 传递详细错误信息
            debugInfo: result.debugInfo, // 传递调试信息
          }
        };

        results[index] = chapterResult;
        completed++;

        // 实时回调章节完成
        if (onChapterComplete) {
          onChapterComplete(index, chapterResult);
        }

        // 更新进度
        const progress = (completed / chapters.length) * 100;
        const totalTime = Date.now() - startTime;
        const averageTimePerChapter = totalTime / completed;

        if (onProgress) {
          onProgress(progress, chapter.number, {
            completed,
            totalTokensUsed,
            totalTime,
            averageTimePerChapter,
            hasContext: !!(novelContext || chapterContext)
          });
        }

        console.log(`第 ${chapter.number} 章重写${result.success ? '成功' : '失败'}${novelContext || chapterContext ? '（使用上下文）' : ''}: ${result.error || '完成'}`);

      } catch (error) {
        const chapterProcessingTime = Date.now() - chapterStartTime;
        const chapterResult = {
          success: false,
          content: '',
          error: `重写异常: ${error instanceof Error ? error.message : '未知错误'}`,
          details: {
            processingTime: chapterProcessingTime,
            chapterNumber: chapter.number,
            chapterTitle: chapter.title,
            hasContext: !!(novelContext || chapterContext)
          }
        };

        results[index] = chapterResult;
        completed++;

        if (onChapterComplete) {
          onChapterComplete(index, chapterResult);
        }

        console.error(`第 ${chapter.number} 章重写异常:`, error);
      } finally {
        release();
      }
    };

    // 并行处理所有章节
    const promises = chapters.map((chapter, index) => processChapter(chapter, index));
    await Promise.all(promises);

    return results;

  } catch (error) {
    console.error('批量重写失败，回退到普通重写:', error);
    // 如果上下文重写失败，回退到普通重写
    const { rewriteChapters } = await import('./gemini');
    return await rewriteChapters(
      chapters,
      rules,
      onProgress,
      onChapterComplete,
      concurrency,
      model,
      enableFailureRecovery
    );
  }
}

// 重新导出客户端函数，提供统一接口
export {
  getNovelContext,
  getChapterContext,
  getChapterContextWindow,
  getNovelContextClient,
  getChapterContextClient,
  getChapterContextWindowClient,
  analyzeNovelClient,
  rewriteTextWithContextClient
} from './context-client';
