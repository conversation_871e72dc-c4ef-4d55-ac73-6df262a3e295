{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/lib/database.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\nimport crypto from 'crypto';\n\n// 数据类型定义\nexport interface Novel {\n  id: string;\n  title: string;\n  filename: string;\n  createdAt: string;\n  chapterCount?: number;\n}\n\nexport interface Chapter {\n  id: string;\n  novelId: string;\n  chapterNumber: number;\n  title: string;\n  content: string;\n  filename: string;\n  createdAt: string;\n}\n\nexport interface RewriteRule {\n  id: string;\n  name: string;\n  description: string;\n  rules: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Character {\n  id: string;\n  novelId: string;\n  name: string;\n  role: string; // 角色类型：男主、女主、配角、反派、其他\n  description: string;\n  personality?: string; // 性格特点\n  appearance?: string; // 外貌描述\n  relationships?: string; // 人物关系\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 小说整体上下文\nexport interface NovelContext {\n  id: string;\n  novelId: string;\n  summary: string; // 小说整体摘要\n  mainCharacters: Array<{\n    name: string;\n    role: string;\n    description: string;\n    relationships?: string;\n  }>; // 主要人物信息\n  worldSetting: string; // 世界观设定\n  writingStyle: string; // 写作风格特征\n  mainPlotlines: string[]; // 主要情节线\n  themes: string[]; // 主题\n  tone: string; // 语调风格\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 章节上下文\nexport interface ChapterContext {\n  id: string;\n  novelId: string;\n  chapterNumber: number;\n  keyEvents: string[]; // 关键事件\n  characterStates: Array<{\n    name: string;\n    status: string; // 人物在本章的状态\n    emotions: string; // 情感状态\n    relationships: string; // 关系变化\n  }>; // 人物状态\n  plotProgress: string; // 情节推进要点\n  previousChapterSummary?: string; // 前一章摘要\n  nextChapterHints?: string; // 对下一章的暗示\n  contextualNotes: string; // 上下文注释\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Preset {\n  id: string;\n  name: string;\n  description: string;\n  rules: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface RewriteJob {\n  id: string;\n  novelId: string;\n  chapters: number[];\n  ruleId: string;\n  status: 'pending' | 'processing' | 'completed' | 'failed';\n  progress: number;\n  result?: string;\n  createdAt: string;\n  updatedAt: string;\n  // 新增详细信息字段\n  details?: {\n    totalChapters: number;\n    completedChapters: number;\n    failedChapters: number;\n    totalTokensUsed: number;\n    totalProcessingTime: number;\n    averageTimePerChapter: number;\n    apiKeyStats: Array<{\n      name: string;\n      requestCount: number;\n      weight: number;\n      isAvailable: boolean;\n    }>;\n    chapterResults: Array<{\n      chapterNumber: number;\n      chapterTitle: string;\n      success: boolean;\n      error?: string;\n      apiKeyUsed?: string;\n      tokensUsed?: number;\n      processingTime?: number;\n      completedAt?: string;\n    }>;\n    model?: string;\n    concurrency?: number;\n  };\n}\n\n// 数据存储路径\nconst DATA_DIR = path.join(process.cwd(), 'data');\nconst NOVELS_FILE = path.join(DATA_DIR, 'novels.json');\nconst CHAPTERS_FILE = path.join(DATA_DIR, 'chapters.json');\nconst RULES_FILE = path.join(DATA_DIR, 'rewrite_rules.json');\nconst JOBS_FILE = path.join(DATA_DIR, 'rewrite_jobs.json');\nconst CHARACTERS_FILE = path.join(DATA_DIR, 'characters.json');\nconst PRESETS_FILE = path.join(DATA_DIR, 'presets.json');\nconst NOVEL_CONTEXTS_FILE = path.join(DATA_DIR, 'novel-contexts.json');\nconst CHAPTER_CONTEXTS_FILE = path.join(DATA_DIR, 'chapter-contexts.json');\n\n// 确保数据目录存在\nfunction ensureDataDir() {\n  if (!fs.existsSync(DATA_DIR)) {\n    fs.mkdirSync(DATA_DIR, { recursive: true });\n  }\n}\n\n// 读取JSON文件\nfunction readJsonFile<T>(filePath: string): T[] {\n  ensureDataDir();\n  if (!fs.existsSync(filePath)) {\n    return [];\n  }\n  try {\n    const data = fs.readFileSync(filePath, 'utf-8');\n    return JSON.parse(data);\n  } catch (error) {\n    console.error(`Error reading ${filePath}:`, error);\n    return [];\n  }\n}\n\n// 写入JSON文件\nfunction writeJsonFile<T>(filePath: string, data: T[]) {\n  ensureDataDir();\n  try {\n    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf-8');\n  } catch (error) {\n    console.error(`Error writing ${filePath}:`, error);\n    throw error;\n  }\n}\n\n// 生成唯一ID\nfunction generateId(): string {\n  return Date.now().toString(36) + Math.random().toString(36).substring(2);\n}\n\n// 基于内容生成确定性ID\nfunction generateDeterministicId(content: string): string {\n  return crypto.createHash('md5').update(content).digest('hex').substring(0, 18);\n}\n\n// 小说相关操作\nexport const novelDb = {\n  getAll: (): Novel[] => readJsonFile<Novel>(NOVELS_FILE),\n\n  getById: (id: string): Novel | undefined => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    return novels.find(novel => novel.id === id);\n  },\n\n  create: (novel: Omit<Novel, 'id' | 'createdAt'>): Novel => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n\n    // 使用书名生成确定性ID\n    const novelId = generateDeterministicId(novel.title);\n\n    // 检查是否已存在相同ID的小说\n    const existingNovel = novels.find(n => n.id === novelId);\n    if (existingNovel) {\n      // 如果已存在，更新现有记录\n      existingNovel.filename = novel.filename;\n      existingNovel.chapterCount = novel.chapterCount;\n      writeJsonFile(NOVELS_FILE, novels);\n      return existingNovel;\n    }\n\n    const newNovel: Novel = {\n      ...novel,\n      id: novelId,\n      createdAt: new Date().toISOString(),\n    };\n    novels.push(newNovel);\n    writeJsonFile(NOVELS_FILE, novels);\n    return newNovel;\n  },\n\n  update: (id: string, updates: Partial<Novel>): Novel | null => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    const index = novels.findIndex(novel => novel.id === id);\n    if (index === -1) return null;\n\n    novels[index] = { ...novels[index], ...updates };\n    writeJsonFile(NOVELS_FILE, novels);\n    return novels[index];\n  },\n\n  delete: (id: string): boolean => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    const index = novels.findIndex(novel => novel.id === id);\n    if (index === -1) return false;\n\n    novels.splice(index, 1);\n    writeJsonFile(NOVELS_FILE, novels);\n    return true;\n  }\n};\n\n// 章节相关操作\nexport const chapterDb = {\n  getAll: (): Chapter[] => readJsonFile<Chapter>(CHAPTERS_FILE),\n\n  getByNovelId: (novelId: string): Chapter[] => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    return chapters.filter(chapter => chapter.novelId === novelId);\n  },\n\n  getById: (id: string): Chapter | undefined => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    return chapters.find(chapter => chapter.id === id);\n  },\n\n  create: (chapter: Omit<Chapter, 'id' | 'createdAt'>): Chapter => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const newChapter: Chapter = {\n      ...chapter,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n    };\n    chapters.push(newChapter);\n    writeJsonFile(CHAPTERS_FILE, chapters);\n    return newChapter;\n  },\n\n  createBatch: (chapters: Omit<Chapter, 'id' | 'createdAt'>[]): Chapter[] => {\n    const existingChapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const newChapters = chapters.map(chapter => ({\n      ...chapter,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n    }));\n    existingChapters.push(...newChapters);\n    writeJsonFile(CHAPTERS_FILE, existingChapters);\n    return newChapters;\n  },\n\n  delete: (id: string): boolean => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const index = chapters.findIndex(chapter => chapter.id === id);\n    if (index === -1) return false;\n\n    chapters.splice(index, 1);\n    writeJsonFile(CHAPTERS_FILE, chapters);\n    return true;\n  },\n\n  deleteByNovelId: (novelId: string): boolean => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const filteredChapters = chapters.filter(chapter => chapter.novelId !== novelId);\n    writeJsonFile(CHAPTERS_FILE, filteredChapters);\n    return true;\n  }\n};\n\n// 改写规则相关操作\nexport const ruleDb = {\n  getAll: (): RewriteRule[] => readJsonFile<RewriteRule>(RULES_FILE),\n\n  getById: (id: string): RewriteRule | undefined => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    return rules.find(rule => rule.id === id);\n  },\n\n  create: (rule: Omit<RewriteRule, 'id' | 'createdAt' | 'updatedAt'>): RewriteRule => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const newRule: RewriteRule = {\n      ...rule,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    rules.push(newRule);\n    writeJsonFile(RULES_FILE, rules);\n    return newRule;\n  },\n\n  update: (id: string, updates: Partial<RewriteRule>): RewriteRule | null => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const index = rules.findIndex(rule => rule.id === id);\n    if (index === -1) return null;\n\n    rules[index] = {\n      ...rules[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(RULES_FILE, rules);\n    return rules[index];\n  },\n\n  delete: (id: string): boolean => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const index = rules.findIndex(rule => rule.id === id);\n    if (index === -1) return false;\n\n    rules.splice(index, 1);\n    writeJsonFile(RULES_FILE, rules);\n    return true;\n  }\n};\n\n// 改写任务相关操作\nexport const jobDb = {\n  getAll: (): RewriteJob[] => readJsonFile<RewriteJob>(JOBS_FILE),\n\n  getById: (id: string): RewriteJob | undefined => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    return jobs.find(job => job.id === id);\n  },\n\n  create: (job: Omit<RewriteJob, 'id' | 'createdAt' | 'updatedAt'>): RewriteJob => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const newJob: RewriteJob = {\n      ...job,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    jobs.push(newJob);\n    writeJsonFile(JOBS_FILE, jobs);\n    return newJob;\n  },\n\n  update: (id: string, updates: Partial<RewriteJob>): RewriteJob | null => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const index = jobs.findIndex(job => job.id === id);\n    if (index === -1) return null;\n\n    jobs[index] = {\n      ...jobs[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(JOBS_FILE, jobs);\n    return jobs[index];\n  },\n\n  delete: (id: string): boolean => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const index = jobs.findIndex(job => job.id === id);\n    if (index === -1) return false;\n\n    jobs.splice(index, 1);\n    writeJsonFile(JOBS_FILE, jobs);\n    return true;\n  }\n};\n\n// 人物设定相关操作\nexport const characterDb = {\n  getAll: (): Character[] => readJsonFile<Character>(CHARACTERS_FILE),\n\n  getByNovelId: (novelId: string): Character[] => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    return characters.filter(character => character.novelId === novelId);\n  },\n\n  getById: (id: string): Character | undefined => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    return characters.find(character => character.id === id);\n  },\n\n  create: (character: Omit<Character, 'id' | 'createdAt' | 'updatedAt'>): Character => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const newCharacter: Character = {\n      ...character,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    characters.push(newCharacter);\n    writeJsonFile(CHARACTERS_FILE, characters);\n    return newCharacter;\n  },\n\n  update: (id: string, updates: Partial<Character>): Character | null => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const index = characters.findIndex(character => character.id === id);\n    if (index === -1) return null;\n\n    characters[index] = {\n      ...characters[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(CHARACTERS_FILE, characters);\n    return characters[index];\n  },\n\n  delete: (id: string): boolean => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const index = characters.findIndex(character => character.id === id);\n    if (index === -1) return false;\n\n    characters.splice(index, 1);\n    writeJsonFile(CHARACTERS_FILE, characters);\n    return true;\n  },\n\n  deleteByNovelId: (novelId: string): boolean => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const filteredCharacters = characters.filter(character => character.novelId !== novelId);\n    writeJsonFile(CHARACTERS_FILE, filteredCharacters);\n    return true;\n  }\n};\n\n// 预设相关操作\nexport const presetDb = {\n  getAll: (): Preset[] => readJsonFile<Preset>(PRESETS_FILE),\n\n  getById: (id: string): Preset | undefined => {\n    const presets = readJsonFile<Preset>(PRESETS_FILE);\n    return presets.find(preset => preset.id === id);\n  },\n\n  create: (preset: Omit<Preset, 'id' | 'createdAt' | 'updatedAt'>): Preset => {\n    const presets = readJsonFile<Preset>(PRESETS_FILE);\n    const newPreset: Preset = {\n      ...preset,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    presets.push(newPreset);\n    writeJsonFile(PRESETS_FILE, presets);\n    return newPreset;\n  },\n\n  update: (id: string, updates: Partial<Preset>): Preset | null => {\n    const presets = readJsonFile<Preset>(PRESETS_FILE);\n    const index = presets.findIndex(preset => preset.id === id);\n    if (index === -1) return null;\n\n    presets[index] = {\n      ...presets[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(PRESETS_FILE, presets);\n    return presets[index];\n  },\n\n  delete: (id: string): boolean => {\n    const presets = readJsonFile<Preset>(PRESETS_FILE);\n    const index = presets.findIndex(preset => preset.id === id);\n    if (index === -1) return false;\n\n    presets.splice(index, 1);\n    writeJsonFile(PRESETS_FILE, presets);\n    return true;\n  }\n};\n\n// 小说上下文相关操作\nexport const novelContextDb = {\n  getAll: (): NovelContext[] => readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE),\n\n  getByNovelId: (novelId: string): NovelContext | undefined => {\n    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);\n    return contexts.find(context => context.novelId === novelId);\n  },\n\n  getById: (id: string): NovelContext | undefined => {\n    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);\n    return contexts.find(context => context.id === id);\n  },\n\n  create: (context: Omit<NovelContext, 'id' | 'createdAt' | 'updatedAt'>): NovelContext => {\n    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);\n    const newContext: NovelContext = {\n      ...context,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    contexts.push(newContext);\n    writeJsonFile(NOVEL_CONTEXTS_FILE, contexts);\n    return newContext;\n  },\n\n  update: (id: string, updates: Partial<Omit<NovelContext, 'id' | 'createdAt'>>): NovelContext | undefined => {\n    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);\n    const index = contexts.findIndex(context => context.id === id);\n    if (index === -1) return undefined;\n\n    contexts[index] = {\n      ...contexts[index],\n      ...updates,\n      updatedAt: new Date().toISOString(),\n    };\n    writeJsonFile(NOVEL_CONTEXTS_FILE, contexts);\n    return contexts[index];\n  },\n\n  delete: (id: string): boolean => {\n    const contexts = readJsonFile<NovelContext>(NOVEL_CONTEXTS_FILE);\n    const index = contexts.findIndex(context => context.id === id);\n    if (index === -1) return false;\n\n    contexts.splice(index, 1);\n    writeJsonFile(NOVEL_CONTEXTS_FILE, contexts);\n    return true;\n  }\n};\n\n// 章节上下文相关操作\nexport const chapterContextDb = {\n  getAll: (): ChapterContext[] => readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE),\n\n  getByNovelId: (novelId: string): ChapterContext[] => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    return contexts.filter(context => context.novelId === novelId);\n  },\n\n  getByChapter: (novelId: string, chapterNumber: number): ChapterContext | undefined => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    return contexts.find(context =>\n      context.novelId === novelId && context.chapterNumber === chapterNumber\n    );\n  },\n\n  getById: (id: string): ChapterContext | undefined => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    return contexts.find(context => context.id === id);\n  },\n\n  create: (context: Omit<ChapterContext, 'id' | 'createdAt' | 'updatedAt'>): ChapterContext => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    const newContext: ChapterContext = {\n      ...context,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    contexts.push(newContext);\n    writeJsonFile(CHAPTER_CONTEXTS_FILE, contexts);\n    return newContext;\n  },\n\n  update: (id: string, updates: Partial<Omit<ChapterContext, 'id' | 'createdAt'>>): ChapterContext | undefined => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    const index = contexts.findIndex(context => context.id === id);\n    if (index === -1) return undefined;\n\n    contexts[index] = {\n      ...contexts[index],\n      ...updates,\n      updatedAt: new Date().toISOString(),\n    };\n    writeJsonFile(CHAPTER_CONTEXTS_FILE, contexts);\n    return contexts[index];\n  },\n\n  delete: (id: string): boolean => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    const index = contexts.findIndex(context => context.id === id);\n    if (index === -1) return false;\n\n    contexts.splice(index, 1);\n    writeJsonFile(CHAPTER_CONTEXTS_FILE, contexts);\n    return true;\n  },\n\n  // 获取章节的上下文窗口（前后几章的上下文）\n  getContextWindow: (novelId: string, chapterNumber: number, windowSize: number = 2): ChapterContext[] => {\n    const contexts = readJsonFile<ChapterContext>(CHAPTER_CONTEXTS_FILE);\n    const novelContexts = contexts.filter(context => context.novelId === novelId);\n\n    const startChapter = Math.max(1, chapterNumber - windowSize);\n    const endChapter = chapterNumber + windowSize;\n\n    return novelContexts.filter(context =>\n      context.chapterNumber >= startChapter && context.chapterNumber <= endChapter\n    ).sort((a, b) => a.chapterNumber - b.chapterNumber);\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;;;;AAmIA,SAAS;AACT,MAAM,WAAW,4GAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAC1C,MAAM,cAAc,4GAAI,CAAC,IAAI,CAAC,UAAU;AACxC,MAAM,gBAAgB,4GAAI,CAAC,IAAI,CAAC,UAAU;AAC1C,MAAM,aAAa,4GAAI,CAAC,IAAI,CAAC,UAAU;AACvC,MAAM,YAAY,4GAAI,CAAC,IAAI,CAAC,UAAU;AACtC,MAAM,kBAAkB,4GAAI,CAAC,IAAI,CAAC,UAAU;AAC5C,MAAM,eAAe,4GAAI,CAAC,IAAI,CAAC,UAAU;AACzC,MAAM,sBAAsB,4GAAI,CAAC,IAAI,CAAC,UAAU;AAChD,MAAM,wBAAwB,4GAAI,CAAC,IAAI,CAAC,UAAU;AAElD,WAAW;AACX,SAAS;IACP,IAAI,CAAC,wGAAE,CAAC,UAAU,CAAC,WAAW;QAC5B,wGAAE,CAAC,SAAS,CAAC,UAAU;YAAE,WAAW;QAAK;IAC3C;AACF;AAEA,WAAW;AACX,SAAS,aAAgB,QAAgB;IACvC;IACA,IAAI,CAAC,wGAAE,CAAC,UAAU,CAAC,WAAW;QAC5B,OAAO,EAAE;IACX;IACA,IAAI;QACF,MAAM,OAAO,wGAAE,CAAC,YAAY,CAAC,UAAU;QACvC,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC,EAAE;QAC5C,OAAO,EAAE;IACX;AACF;AAEA,WAAW;AACX,SAAS,cAAiB,QAAgB,EAAE,IAAS;IACnD;IACA,IAAI;QACF,wGAAE,CAAC,aAAa,CAAC,UAAU,KAAK,SAAS,CAAC,MAAM,MAAM,IAAI;IAC5D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC,EAAE;QAC5C,MAAM;IACR;AACF;AAEA,SAAS;AACT,SAAS;IACP,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC;AACxE;AAEA,cAAc;AACd,SAAS,wBAAwB,OAAe;IAC9C,OAAO,gHAAM,CAAC,UAAU,CAAC,OAAO,MAAM,CAAC,SAAS,MAAM,CAAC,OAAO,SAAS,CAAC,GAAG;AAC7E;AAGO,MAAM,UAAU;IACrB,QAAQ,IAAe,aAAoB;IAE3C,SAAS,CAAC;QACR,MAAM,SAAS,aAAoB;QACnC,OAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAC3C;IAEA,QAAQ,CAAC;QACP,MAAM,SAAS,aAAoB;QAEnC,cAAc;QACd,MAAM,UAAU,wBAAwB,MAAM,KAAK;QAEnD,iBAAiB;QACjB,MAAM,gBAAgB,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAChD,IAAI,eAAe;YACjB,eAAe;YACf,cAAc,QAAQ,GAAG,MAAM,QAAQ;YACvC,cAAc,YAAY,GAAG,MAAM,YAAY;YAC/C,cAAc,aAAa;YAC3B,OAAO;QACT;QAEA,MAAM,WAAkB;YACtB,GAAG,KAAK;YACR,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,OAAO,IAAI,CAAC;QACZ,cAAc,aAAa;QAC3B,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,SAAS,aAAoB;QACnC,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QACrD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,MAAM,CAAC,MAAM,GAAG;YAAE,GAAG,MAAM,CAAC,MAAM;YAAE,GAAG,OAAO;QAAC;QAC/C,cAAc,aAAa;QAC3B,OAAO,MAAM,CAAC,MAAM;IACtB;IAEA,QAAQ,CAAC;QACP,MAAM,SAAS,aAAoB;QACnC,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QACrD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,OAAO,MAAM,CAAC,OAAO;QACrB,cAAc,aAAa;QAC3B,OAAO;IACT;AACF;AAGO,MAAM,YAAY;IACvB,QAAQ,IAAiB,aAAsB;IAE/C,cAAc,CAAC;QACb,MAAM,WAAW,aAAsB;QACvC,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;IACxD;IAEA,SAAS,CAAC;QACR,MAAM,WAAW,aAAsB;QACvC,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IACjD;IAEA,QAAQ,CAAC;QACP,MAAM,WAAW,aAAsB;QACvC,MAAM,aAAsB;YAC1B,GAAG,OAAO;YACV,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,SAAS,IAAI,CAAC;QACd,cAAc,eAAe;QAC7B,OAAO;IACT;IAEA,aAAa,CAAC;QACZ,MAAM,mBAAmB,aAAsB;QAC/C,MAAM,cAAc,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;gBAC3C,GAAG,OAAO;gBACV,IAAI;gBACJ,WAAW,IAAI,OAAO,WAAW;YACnC,CAAC;QACD,iBAAiB,IAAI,IAAI;QACzB,cAAc,eAAe;QAC7B,OAAO;IACT;IAEA,QAAQ,CAAC;QACP,MAAM,WAAW,aAAsB;QACvC,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;QAC3D,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,SAAS,MAAM,CAAC,OAAO;QACvB,cAAc,eAAe;QAC7B,OAAO;IACT;IAEA,iBAAiB,CAAC;QAChB,MAAM,WAAW,aAAsB;QACvC,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;QACxE,cAAc,eAAe;QAC7B,OAAO;IACT;AACF;AAGO,MAAM,SAAS;IACpB,QAAQ,IAAqB,aAA0B;IAEvD,SAAS,CAAC;QACR,MAAM,QAAQ,aAA0B;QACxC,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACxC;IAEA,QAAQ,CAAC;QACP,MAAM,QAAQ,aAA0B;QACxC,MAAM,UAAuB;YAC3B,GAAG,IAAI;YACP,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,MAAM,IAAI,CAAC;QACX,cAAc,YAAY;QAC1B,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,QAAQ,aAA0B;QACxC,MAAM,QAAQ,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAClD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,KAAK,CAAC,MAAM,GAAG;YACb,GAAG,KAAK,CAAC,MAAM;YACf,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,cAAc,YAAY;QAC1B,OAAO,KAAK,CAAC,MAAM;IACrB;IAEA,QAAQ,CAAC;QACP,MAAM,QAAQ,aAA0B;QACxC,MAAM,QAAQ,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAClD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,MAAM,MAAM,CAAC,OAAO;QACpB,cAAc,YAAY;QAC1B,OAAO;IACT;AACF;AAGO,MAAM,QAAQ;IACnB,QAAQ,IAAoB,aAAyB;IAErD,SAAS,CAAC;QACR,MAAM,OAAO,aAAyB;QACtC,OAAO,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;IACrC;IAEA,QAAQ,CAAC;QACP,MAAM,OAAO,aAAyB;QACtC,MAAM,SAAqB;YACzB,GAAG,GAAG;YACN,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,KAAK,IAAI,CAAC;QACV,cAAc,WAAW;QACzB,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,OAAO,aAAyB;QACtC,MAAM,QAAQ,KAAK,SAAS,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAC/C,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,IAAI,CAAC,MAAM,GAAG;YACZ,GAAG,IAAI,CAAC,MAAM;YACd,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,cAAc,WAAW;QACzB,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA,QAAQ,CAAC;QACP,MAAM,OAAO,aAAyB;QACtC,MAAM,QAAQ,KAAK,SAAS,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAC/C,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,KAAK,MAAM,CAAC,OAAO;QACnB,cAAc,WAAW;QACzB,OAAO;IACT;AACF;AAGO,MAAM,cAAc;IACzB,QAAQ,IAAmB,aAAwB;IAEnD,cAAc,CAAC;QACb,MAAM,aAAa,aAAwB;QAC3C,OAAO,WAAW,MAAM,CAAC,CAAA,YAAa,UAAU,OAAO,KAAK;IAC9D;IAEA,SAAS,CAAC;QACR,MAAM,aAAa,aAAwB;QAC3C,OAAO,WAAW,IAAI,CAAC,CAAA,YAAa,UAAU,EAAE,KAAK;IACvD;IAEA,QAAQ,CAAC;QACP,MAAM,aAAa,aAAwB;QAC3C,MAAM,eAA0B;YAC9B,GAAG,SAAS;YACZ,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,WAAW,IAAI,CAAC;QAChB,cAAc,iBAAiB;QAC/B,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,aAAa,aAAwB;QAC3C,MAAM,QAAQ,WAAW,SAAS,CAAC,CAAA,YAAa,UAAU,EAAE,KAAK;QACjE,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,UAAU,CAAC,MAAM,GAAG;YAClB,GAAG,UAAU,CAAC,MAAM;YACpB,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,cAAc,iBAAiB;QAC/B,OAAO,UAAU,CAAC,MAAM;IAC1B;IAEA,QAAQ,CAAC;QACP,MAAM,aAAa,aAAwB;QAC3C,MAAM,QAAQ,WAAW,SAAS,CAAC,CAAA,YAAa,UAAU,EAAE,KAAK;QACjE,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,WAAW,MAAM,CAAC,OAAO;QACzB,cAAc,iBAAiB;QAC/B,OAAO;IACT;IAEA,iBAAiB,CAAC;QAChB,MAAM,aAAa,aAAwB;QAC3C,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAA,YAAa,UAAU,OAAO,KAAK;QAChF,cAAc,iBAAiB;QAC/B,OAAO;IACT;AACF;AAGO,MAAM,WAAW;IACtB,QAAQ,IAAgB,aAAqB;IAE7C,SAAS,CAAC;QACR,MAAM,UAAU,aAAqB;QACrC,OAAO,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;IAC9C;IAEA,QAAQ,CAAC;QACP,MAAM,UAAU,aAAqB;QACrC,MAAM,YAAoB;YACxB,GAAG,MAAM;YACT,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,QAAQ,IAAI,CAAC;QACb,cAAc,cAAc;QAC5B,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,UAAU,aAAqB;QACrC,MAAM,QAAQ,QAAQ,SAAS,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;QACxD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,OAAO,CAAC,MAAM,GAAG;YACf,GAAG,OAAO,CAAC,MAAM;YACjB,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,cAAc,cAAc;QAC5B,OAAO,OAAO,CAAC,MAAM;IACvB;IAEA,QAAQ,CAAC;QACP,MAAM,UAAU,aAAqB;QACrC,MAAM,QAAQ,QAAQ,SAAS,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;QACxD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,QAAQ,MAAM,CAAC,OAAO;QACtB,cAAc,cAAc;QAC5B,OAAO;IACT;AACF;AAGO,MAAM,iBAAiB;IAC5B,QAAQ,IAAsB,aAA2B;IAEzD,cAAc,CAAC;QACb,MAAM,WAAW,aAA2B;QAC5C,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;IACtD;IAEA,SAAS,CAAC;QACR,MAAM,WAAW,aAA2B;QAC5C,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IACjD;IAEA,QAAQ,CAAC;QACP,MAAM,WAAW,aAA2B;QAC5C,MAAM,aAA2B;YAC/B,GAAG,OAAO;YACV,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,SAAS,IAAI,CAAC;QACd,cAAc,qBAAqB;QACnC,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,WAAW,aAA2B;QAC5C,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;QAC3D,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,QAAQ,CAAC,MAAM,GAAG;YAChB,GAAG,QAAQ,CAAC,MAAM;YAClB,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,cAAc,qBAAqB;QACnC,OAAO,QAAQ,CAAC,MAAM;IACxB;IAEA,QAAQ,CAAC;QACP,MAAM,WAAW,aAA2B;QAC5C,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;QAC3D,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,SAAS,MAAM,CAAC,OAAO;QACvB,cAAc,qBAAqB;QACnC,OAAO;IACT;AACF;AAGO,MAAM,mBAAmB;IAC9B,QAAQ,IAAwB,aAA6B;IAE7D,cAAc,CAAC;QACb,MAAM,WAAW,aAA6B;QAC9C,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;IACxD;IAEA,cAAc,CAAC,SAAiB;QAC9B,MAAM,WAAW,aAA6B;QAC9C,OAAO,SAAS,IAAI,CAAC,CAAA,UACnB,QAAQ,OAAO,KAAK,WAAW,QAAQ,aAAa,KAAK;IAE7D;IAEA,SAAS,CAAC;QACR,MAAM,WAAW,aAA6B;QAC9C,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IACjD;IAEA,QAAQ,CAAC;QACP,MAAM,WAAW,aAA6B;QAC9C,MAAM,aAA6B;YACjC,GAAG,OAAO;YACV,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,SAAS,IAAI,CAAC;QACd,cAAc,uBAAuB;QACrC,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,WAAW,aAA6B;QAC9C,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;QAC3D,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,QAAQ,CAAC,MAAM,GAAG;YAChB,GAAG,QAAQ,CAAC,MAAM;YAClB,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,cAAc,uBAAuB;QACrC,OAAO,QAAQ,CAAC,MAAM;IACxB;IAEA,QAAQ,CAAC;QACP,MAAM,WAAW,aAA6B;QAC9C,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;QAC3D,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,SAAS,MAAM,CAAC,OAAO;QACvB,cAAc,uBAAuB;QACrC,OAAO;IACT;IAEA,uBAAuB;IACvB,kBAAkB,CAAC,SAAiB,eAAuB,aAAqB,CAAC;QAC/E,MAAM,WAAW,aAA6B;QAC9C,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;QAErE,MAAM,eAAe,KAAK,GAAG,CAAC,GAAG,gBAAgB;QACjD,MAAM,aAAa,gBAAgB;QAEnC,OAAO,cAAc,MAAM,CAAC,CAAA,UAC1B,QAAQ,aAAa,IAAI,gBAAgB,QAAQ,aAAa,IAAI,YAClE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,aAAa,GAAG,EAAE,aAAa;IACpD;AACF", "debugId": null}}, {"offset": {"line": 510, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/lib/context-client.ts"], "sourcesContent": ["// 客户端上下文工具函数\nimport type { NovelContext, ChapterContext } from './database';\n\n// 检查是否在客户端环境\nfunction isClientSide(): boolean {\n  return typeof window !== 'undefined';\n}\n\n// 客户端获取小说上下文\nexport async function getNovelContextClient(novelId: string): Promise<NovelContext | null> {\n  if (!isClientSide()) {\n    throw new Error('This function can only be used on client side');\n  }\n\n  try {\n    const response = await fetch(`/api/context/novel?novelId=${novelId}`);\n    const result = await response.json();\n    \n    if (!result.success) {\n      console.warn('获取小说上下文失败:', result.error);\n      return null;\n    }\n\n    return result.data;\n  } catch (error) {\n    console.error('获取小说上下文失败:', error);\n    return null;\n  }\n}\n\n// 客户端获取章节上下文\nexport async function getChapterContextClient(novelId: string, chapterNumber: number): Promise<ChapterContext | null> {\n  if (!isClientSide()) {\n    throw new Error('This function can only be used on client side');\n  }\n\n  try {\n    const response = await fetch(`/api/context/chapter?novelId=${novelId}&chapterNumber=${chapterNumber}`);\n    const result = await response.json();\n    \n    if (!result.success) {\n      console.warn('获取章节上下文失败:', result.error);\n      return null;\n    }\n\n    return result.data;\n  } catch (error) {\n    console.error('获取章节上下文失败:', error);\n    return null;\n  }\n}\n\n// 客户端获取章节上下文窗口\nexport async function getChapterContextWindowClient(\n  novelId: string,\n  chapterNumber: number,\n  windowSize: number = 2\n): Promise<ChapterContext[]> {\n  if (!isClientSide()) {\n    throw new Error('This function can only be used on client side');\n  }\n\n  try {\n    const response = await fetch(`/api/context/window?novelId=${novelId}&chapterNumber=${chapterNumber}&windowSize=${windowSize}`);\n    const result = await response.json();\n    \n    if (!result.success) {\n      console.warn('获取章节上下文窗口失败:', result.error);\n      return [];\n    }\n\n    return result.data.contexts;\n  } catch (error) {\n    console.error('获取章节上下文窗口失败:', error);\n    return [];\n  }\n}\n\n// 客户端分析小说上下文\nexport async function analyzeNovelClient(novelId: string, analyzeChapters: boolean = false): Promise<{\n  novelContext: NovelContext;\n  chapterContexts?: ChapterContext[];\n}> {\n  if (!isClientSide()) {\n    throw new Error('This function can only be used on client side');\n  }\n\n  const response = await fetch('/api/context/analyze', {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify({ \n      novelId: novelId,\n      analyzeChapters: analyzeChapters \n    })\n  });\n\n  const result = await response.json();\n  \n  if (!result.success) {\n    throw new Error(result.error || '分析失败');\n  }\n\n  return {\n    novelContext: result.data.novelContext,\n    chapterContexts: result.data.chapterContexts\n  };\n}\n\n// 客户端带上下文的重写函数\nexport async function rewriteTextWithContextClient(\n  novelId: string,\n  chapterNumbers: number[],\n  rules: string,\n  model?: string,\n  onProgress?: (progress: number, currentChapter: number) => void\n): Promise<Array<{ success: boolean; content: string; error?: string }>> {\n  if (!isClientSide()) {\n    throw new Error('This function can only be used on client side');\n  }\n\n  const response = await fetch('/api/rewrite', {\n    method: 'POST',\n    headers: { 'Content-Type': 'application/json' },\n    body: JSON.stringify({\n      novelId: novelId,\n      chapterNumbers: chapterNumbers,\n      rules: rules,\n      model: model || 'gemini-2.5-flash-lite'\n    })\n  });\n\n  const result = await response.json();\n  \n  if (!result.success) {\n    throw new Error(result.error || '重写失败');\n  }\n\n  return result.data.results;\n}\n\n// 通用的上下文获取函数（自动选择服务端或客户端）\nexport async function getNovelContext(novelId: string): Promise<NovelContext | null> {\n  if (isClientSide()) {\n    return getNovelContextClient(novelId);\n  } else {\n    // 服务端环境，动态导入服务端函数\n    const { getNovelContextServer } = await import('./context-utils');\n    return getNovelContextServer(novelId);\n  }\n}\n\n// 通用的章节上下文获取函数（自动选择服务端或客户端）\nexport async function getChapterContext(novelId: string, chapterNumber: number): Promise<ChapterContext | null> {\n  if (isClientSide()) {\n    return getChapterContextClient(novelId, chapterNumber);\n  } else {\n    // 服务端环境，动态导入服务端函数\n    const { getChapterContextServer } = await import('./context-utils');\n    return getChapterContextServer(novelId, chapterNumber);\n  }\n}\n\n// 通用的章节上下文窗口获取函数（自动选择服务端或客户端）\nexport async function getChapterContextWindow(\n  novelId: string,\n  chapterNumber: number,\n  windowSize: number = 2\n): Promise<ChapterContext[]> {\n  if (isClientSide()) {\n    return getChapterContextWindowClient(novelId, chapterNumber, windowSize);\n  } else {\n    // 服务端环境，动态导入服务端函数\n    const { getChapterContextWindowServer } = await import('./context-utils');\n    return getChapterContextWindowServer(novelId, chapterNumber, windowSize);\n  }\n}\n"], "names": [], "mappings": "AAAA,aAAa;;;;;;;;;;;;;;;;;;;AAGb,aAAa;AACb,SAAS;IACP,OAAO,gBAAkB;AAC3B;AAGO,eAAe,sBAAsB,OAAe;IACzD,IAAI,CAAC,gBAAgB;QACnB,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,CAAC,2BAA2B,EAAE,SAAS;QACpE,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,QAAQ,IAAI,CAAC,cAAc,OAAO,KAAK;YACvC,OAAO;QACT;QAEA,OAAO,OAAO,IAAI;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO;IACT;AACF;AAGO,eAAe,wBAAwB,OAAe,EAAE,aAAqB;IAClF,IAAI,CAAC,gBAAgB;QACnB,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,CAAC,6BAA6B,EAAE,QAAQ,eAAe,EAAE,eAAe;QACrG,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,QAAQ,IAAI,CAAC,cAAc,OAAO,KAAK;YACvC,OAAO;QACT;QAEA,OAAO,OAAO,IAAI;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO;IACT;AACF;AAGO,eAAe,8BACpB,OAAe,EACf,aAAqB,EACrB,aAAqB,CAAC;IAEtB,IAAI,CAAC,gBAAgB;QACnB,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,CAAC,4BAA4B,EAAE,QAAQ,eAAe,EAAE,cAAc,YAAY,EAAE,YAAY;QAC7H,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,QAAQ,IAAI,CAAC,gBAAgB,OAAO,KAAK;YACzC,OAAO,EAAE;QACX;QAEA,OAAO,OAAO,IAAI,CAAC,QAAQ;IAC7B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO,EAAE;IACX;AACF;AAGO,eAAe,mBAAmB,OAAe,EAAE,kBAA2B,KAAK;IAIxF,IAAI,CAAC,gBAAgB;QACnB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,WAAW,MAAM,MAAM,wBAAwB;QACnD,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,MAAM,KAAK,SAAS,CAAC;YACnB,SAAS;YACT,iBAAiB;QACnB;IACF;IAEA,MAAM,SAAS,MAAM,SAAS,IAAI;IAElC,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;IAClC;IAEA,OAAO;QACL,cAAc,OAAO,IAAI,CAAC,YAAY;QACtC,iBAAiB,OAAO,IAAI,CAAC,eAAe;IAC9C;AACF;AAGO,eAAe,6BACpB,OAAe,EACf,cAAwB,EACxB,KAAa,EACb,KAAc,EACd,UAA+D;IAE/D,IAAI,CAAC,gBAAgB;QACnB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,WAAW,MAAM,MAAM,gBAAgB;QAC3C,QAAQ;QACR,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,MAAM,KAAK,SAAS,CAAC;YACnB,SAAS;YACT,gBAAgB;YAChB,OAAO;YACP,OAAO,SAAS;QAClB;IACF;IAEA,MAAM,SAAS,MAAM,SAAS,IAAI;IAElC,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;IAClC;IAEA,OAAO,OAAO,IAAI,CAAC,OAAO;AAC5B;AAGO,eAAe,gBAAgB,OAAe;IACnD,IAAI;;SAEG;QACL,kBAAkB;QAClB,MAAM,EAAE,qBAAqB,EAAE,GAAG;QAClC,OAAO,sBAAsB;IAC/B;AACF;AAGO,eAAe,kBAAkB,OAAe,EAAE,aAAqB;IAC5E,IAAI;;SAEG;QACL,kBAAkB;QAClB,MAAM,EAAE,uBAAuB,EAAE,GAAG;QACpC,OAAO,wBAAwB,SAAS;IAC1C;AACF;AAGO,eAAe,wBACpB,OAAe,EACf,aAAqB,EACrB,aAAqB,CAAC;IAEtB,IAAI;;SAEG;QACL,kBAAkB;QAClB,MAAM,EAAE,6BAA6B,EAAE,GAAG;QAC1C,OAAO,8BAA8B,SAAS,eAAe;IAC/D;AACF", "debugId": null}}, {"offset": {"line": 660, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/lib/context-utils.ts"], "sourcesContent": ["// 上下文工具函数 - 服务端和客户端通用\nimport type { RewriteRequest, RewriteResponse } from './gemini';\n\n// 检查是否在服务端环境\nfunction isServerSide(): boolean {\n  return typeof window === 'undefined';\n}\n\n// 带上下文的重写函数（服务端专用）\nexport async function rewriteTextWithContextServer(\n  novelId: string,\n  chapterNumber: number,\n  originalText: string,\n  rules: string,\n  chapterTitle?: string,\n  model?: string\n): Promise<RewriteResponse> {\n  if (!isServerSide()) {\n    throw new Error('This function can only be used on server side');\n  }\n\n  try {\n    // 导入服务端模块\n    const { rewriteText } = await import('./gemini');\n    const { novelContextDb, chapterContextDb } = await import('./database');\n\n    // 获取小说整体上下文\n    const novelContext = novelContextDb.getByNovelId(novelId);\n\n    // 获取章节上下文\n    const chapterContext = chapterContextDb.getByChapter(novelId, chapterNumber);\n\n    // 构建请求\n    const request: RewriteRequest = {\n      originalText,\n      rules,\n      chapterTitle,\n      chapterNumber,\n      model,\n      novelContext: novelContext ? {\n        summary: novelContext.summary,\n        mainCharacters: novelContext.mainCharacters,\n        worldSetting: novelContext.worldSetting,\n        writingStyle: novelContext.writingStyle,\n        tone: novelContext.tone\n      } : undefined,\n      chapterContext: chapterContext ? {\n        previousChapterSummary: chapterContext.previousChapterSummary,\n        keyEvents: chapterContext.keyEvents,\n        characterStates: chapterContext.characterStates,\n        plotProgress: chapterContext.plotProgress,\n        contextualNotes: chapterContext.contextualNotes\n      } : undefined\n    };\n\n    return await rewriteText(request);\n  } catch (error) {\n    console.error('带上下文重写失败:', error);\n    // 如果获取上下文失败，回退到普通重写\n    const { rewriteText } = await import('./gemini');\n    return await rewriteText({\n      originalText,\n      rules,\n      chapterTitle,\n      chapterNumber,\n      model\n    });\n  }\n}\n\n// 获取小说上下文（服务端专用）\nexport async function getNovelContextServer(novelId: string) {\n  if (!isServerSide()) {\n    throw new Error('This function can only be used on server side');\n  }\n\n  const { novelContextDb } = await import('./database');\n  return novelContextDb.getByNovelId(novelId);\n}\n\n// 获取章节上下文（服务端专用）\nexport async function getChapterContextServer(novelId: string, chapterNumber: number) {\n  if (!isServerSide()) {\n    throw new Error('This function can only be used on server side');\n  }\n\n  const { chapterContextDb } = await import('./database');\n  return chapterContextDb.getByChapter(novelId, chapterNumber);\n}\n\n// 获取章节上下文窗口（服务端专用）\nexport async function getChapterContextWindowServer(\n  novelId: string,\n  chapterNumber: number,\n  windowSize: number = 2\n) {\n  if (!isServerSide()) {\n    throw new Error('This function can only be used on server side');\n  }\n\n  const { chapterContextDb } = await import('./database');\n  return chapterContextDb.getContextWindow(novelId, chapterNumber, windowSize);\n}\n\n// 带上下文的批量重写函数（服务端专用）\nexport async function rewriteChaptersWithContext(\n  novelId: string,\n  chapters: Array<{ content: string; title: string; number: number }>,\n  rules: string,\n  onProgress?: (progress: number, currentChapter: number, details?: any) => void,\n  onChapterComplete?: (chapterIndex: number, result: any) => void,\n  concurrency: number = 3,\n  model: string = 'gemini-2.5-flash-lite',\n  enableFailureRecovery: boolean = true\n): Promise<Array<{ success: boolean; content: string; error?: string; details?: any }>> {\n  if (!isServerSide()) {\n    throw new Error('This function can only be used on server side');\n  }\n\n  try {\n    // 导入所需模块\n    const { novelContextDb, chapterContextDb } = await import('./database');\n    const { rewriteText } = await import('./gemini');\n\n    // 获取小说整体上下文\n    const novelContext = novelContextDb.getByNovelId(novelId);\n\n    const results: Array<{ success: boolean; content: string; error?: string; details?: any }> = new Array(chapters.length);\n    let completed = 0;\n    let totalTokensUsed = 0;\n    const startTime = Date.now();\n\n    // 使用信号量控制并发\n    const semaphore = { count: concurrency, waiting: [] as Array<() => void> };\n\n    const acquire = () => new Promise<void>(resolve => {\n      if (semaphore.count > 0) {\n        semaphore.count--;\n        resolve();\n      } else {\n        semaphore.waiting.push(resolve);\n      }\n    });\n\n    const release = () => {\n      semaphore.count++;\n      if (semaphore.waiting.length > 0) {\n        const next = semaphore.waiting.shift();\n        if (next) {\n          semaphore.count--;\n          next();\n        }\n      }\n    };\n\n    const processChapter = async (chapter: { content: string; title: string; number: number }, index: number) => {\n      await acquire();\n      const chapterStartTime = Date.now();\n\n      try {\n        // 获取章节上下文\n        const chapterContext = chapterContextDb.getByChapter(novelId, chapter.number);\n\n        // 构建带上下文的请求\n        const request: RewriteRequest = {\n          originalText: chapter.content,\n          rules,\n          chapterTitle: chapter.title,\n          chapterNumber: chapter.number,\n          model,\n          novelContext: novelContext ? {\n            summary: novelContext.summary,\n            mainCharacters: novelContext.mainCharacters,\n            worldSetting: novelContext.worldSetting,\n            writingStyle: novelContext.writingStyle,\n            tone: novelContext.tone\n          } : undefined,\n          chapterContext: chapterContext ? {\n            previousChapterSummary: chapterContext.previousChapterSummary,\n            keyEvents: chapterContext.keyEvents,\n            characterStates: chapterContext.characterStates,\n            plotProgress: chapterContext.plotProgress,\n            contextualNotes: chapterContext.contextualNotes\n          } : undefined\n        };\n\n        const result = await rewriteText(request);\n        const chapterProcessingTime = Date.now() - chapterStartTime;\n\n        if (result.tokensUsed) {\n          totalTokensUsed += result.tokensUsed;\n        }\n\n        const chapterResult = {\n          success: result.success,\n          content: result.rewrittenText,\n          error: result.error,\n          details: {\n            apiKeyUsed: result.apiKeyUsed,\n            tokensUsed: result.tokensUsed,\n            model: result.model,\n            processingTime: chapterProcessingTime,\n            chapterNumber: chapter.number,\n            chapterTitle: chapter.title,\n            hasContext: !!(novelContext || chapterContext),\n            detailedError: result.detailedError, // 传递详细错误信息\n            debugInfo: result.debugInfo, // 传递调试信息\n          }\n        };\n\n        results[index] = chapterResult;\n        completed++;\n\n        // 实时回调章节完成\n        if (onChapterComplete) {\n          onChapterComplete(index, chapterResult);\n        }\n\n        // 更新进度\n        const progress = (completed / chapters.length) * 100;\n        const totalTime = Date.now() - startTime;\n        const averageTimePerChapter = totalTime / completed;\n\n        if (onProgress) {\n          onProgress(progress, chapter.number, {\n            completed,\n            totalTokensUsed,\n            totalTime,\n            averageTimePerChapter,\n            hasContext: !!(novelContext || chapterContext)\n          });\n        }\n\n        console.log(`第 ${chapter.number} 章重写${result.success ? '成功' : '失败'}${novelContext || chapterContext ? '（使用上下文）' : ''}: ${result.error || '完成'}`);\n\n      } catch (error) {\n        const chapterProcessingTime = Date.now() - chapterStartTime;\n        const chapterResult = {\n          success: false,\n          content: '',\n          error: `重写异常: ${error instanceof Error ? error.message : '未知错误'}`,\n          details: {\n            processingTime: chapterProcessingTime,\n            chapterNumber: chapter.number,\n            chapterTitle: chapter.title,\n            hasContext: !!(novelContext || chapterContext)\n          }\n        };\n\n        results[index] = chapterResult;\n        completed++;\n\n        if (onChapterComplete) {\n          onChapterComplete(index, chapterResult);\n        }\n\n        console.error(`第 ${chapter.number} 章重写异常:`, error);\n      } finally {\n        release();\n      }\n    };\n\n    // 并行处理所有章节\n    const promises = chapters.map((chapter, index) => processChapter(chapter, index));\n    await Promise.all(promises);\n\n    return results;\n\n  } catch (error) {\n    console.error('批量重写失败，回退到普通重写:', error);\n    // 如果上下文重写失败，回退到普通重写\n    const { rewriteChapters } = await import('./gemini');\n    return await rewriteChapters(\n      chapters,\n      rules,\n      onProgress,\n      onChapterComplete,\n      concurrency,\n      model,\n      enableFailureRecovery\n    );\n  }\n}\n\n// 重新导出客户端函数，提供统一接口\nexport {\n  getNovelContext,\n  getChapterContext,\n  getChapterContextWindow,\n  getNovelContextClient,\n  getChapterContextClient,\n  getChapterContextWindowClient,\n  analyzeNovelClient,\n  rewriteTextWithContextClient\n} from './context-client';\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;;;;;;;;;AA4RtB,mBAAmB;AACnB;AA1RA,aAAa;AACb,SAAS;IACP,OAAO,gBAAkB;AAC3B;AAGO,eAAe,6BACpB,OAAe,EACf,aAAqB,EACrB,YAAoB,EACpB,KAAa,EACb,YAAqB,EACrB,KAAc;IAEd,IAAI,CAAC;;IAIL,IAAI;QACF,UAAU;QACV,MAAM,EAAE,WAAW,EAAE,GAAG;QACxB,MAAM,EAAE,cAAc,EAAE,gBAAgB,EAAE,GAAG;QAE7C,YAAY;QACZ,MAAM,eAAe,eAAe,YAAY,CAAC;QAEjD,UAAU;QACV,MAAM,kBAAiB,iBAAiB,YAAY,CAAC,SAAS;QAE9D,OAAO;QACP,MAAM,UAA0B;YAC9B;YACA;YACA;YACA;YACA;YACA,cAAc,eAAe;gBAC3B,SAAS,aAAa,OAAO;gBAC7B,gBAAgB,aAAa,cAAc;gBAC3C,cAAc,aAAa,YAAY;gBACvC,cAAc,aAAa,YAAY;gBACvC,MAAM,aAAa,IAAI;YACzB,IAAI;YACJ,gBAAgB,kBAAiB;gBAC/B,wBAAwB,gBAAe,sBAAsB;gBAC7D,WAAW,gBAAe,SAAS;gBACnC,iBAAiB,gBAAe,eAAe;gBAC/C,cAAc,gBAAe,YAAY;gBACzC,iBAAiB,gBAAe,eAAe;YACjD,IAAI;QACN;QAEA,OAAO,MAAM,YAAY;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,oBAAoB;QACpB,MAAM,EAAE,WAAW,EAAE,GAAG;QACxB,OAAO,MAAM,YAAY;YACvB;YACA;YACA;YACA;YACA;QACF;IACF;AACF;AAGO,eAAe,sBAAsB,OAAe;IACzD,IAAI,CAAC;;IAIL,MAAM,EAAE,cAAc,EAAE,GAAG;IAC3B,OAAO,eAAe,YAAY,CAAC;AACrC;AAGO,eAAe,wBAAwB,OAAe,EAAE,aAAqB;IAClF,IAAI,CAAC;;IAIL,MAAM,EAAE,gBAAgB,EAAE,GAAG;IAC7B,OAAO,iBAAiB,YAAY,CAAC,SAAS;AAChD;AAGO,eAAe,8BACpB,OAAe,EACf,aAAqB,EACrB,aAAqB,CAAC;IAEtB,IAAI,CAAC;;IAIL,MAAM,EAAE,gBAAgB,EAAE,GAAG;IAC7B,OAAO,iBAAiB,gBAAgB,CAAC,SAAS,eAAe;AACnE;AAGO,eAAe,2BACpB,OAAe,EACf,QAAmE,EACnE,KAAa,EACb,UAA8E,EAC9E,iBAA+D,EAC/D,cAAsB,CAAC,EACvB,QAAgB,uBAAuB,EACvC,wBAAiC,IAAI;IAErC,IAAI,CAAC;;IAIL,IAAI;QACF,SAAS;QACT,MAAM,EAAE,cAAc,EAAE,gBAAgB,EAAE,GAAG;QAC7C,MAAM,EAAE,WAAW,EAAE,GAAG;QAExB,YAAY;QACZ,MAAM,eAAe,eAAe,YAAY,CAAC;QAEjD,MAAM,UAAuF,IAAI,MAAM,SAAS,MAAM;QACtH,IAAI,YAAY;QAChB,IAAI,kBAAkB;QACtB,MAAM,YAAY,KAAK,GAAG;QAE1B,YAAY;QACZ,MAAM,YAAY;YAAE,OAAO;YAAa,SAAS,EAAE;QAAsB;QAEzE,MAAM,UAAU,IAAM,IAAI,QAAc,CAAA;gBACtC,IAAI,UAAU,KAAK,GAAG,GAAG;oBACvB,UAAU,KAAK;oBACf;gBACF,OAAO;oBACL,UAAU,OAAO,CAAC,IAAI,CAAC;gBACzB;YACF;QAEA,MAAM,UAAU;YACd,UAAU,KAAK;YACf,IAAI,UAAU,OAAO,CAAC,MAAM,GAAG,GAAG;gBAChC,MAAM,OAAO,UAAU,OAAO,CAAC,KAAK;gBACpC,IAAI,MAAM;oBACR,UAAU,KAAK;oBACf;gBACF;YACF;QACF;QAEA,MAAM,iBAAiB,OAAO,SAA6D;YACzF,MAAM;YACN,MAAM,mBAAmB,KAAK,GAAG;YAEjC,IAAI;gBACF,UAAU;gBACV,MAAM,kBAAiB,iBAAiB,YAAY,CAAC,SAAS,QAAQ,MAAM;gBAE5E,YAAY;gBACZ,MAAM,UAA0B;oBAC9B,cAAc,QAAQ,OAAO;oBAC7B;oBACA,cAAc,QAAQ,KAAK;oBAC3B,eAAe,QAAQ,MAAM;oBAC7B;oBACA,cAAc,eAAe;wBAC3B,SAAS,aAAa,OAAO;wBAC7B,gBAAgB,aAAa,cAAc;wBAC3C,cAAc,aAAa,YAAY;wBACvC,cAAc,aAAa,YAAY;wBACvC,MAAM,aAAa,IAAI;oBACzB,IAAI;oBACJ,gBAAgB,kBAAiB;wBAC/B,wBAAwB,gBAAe,sBAAsB;wBAC7D,WAAW,gBAAe,SAAS;wBACnC,iBAAiB,gBAAe,eAAe;wBAC/C,cAAc,gBAAe,YAAY;wBACzC,iBAAiB,gBAAe,eAAe;oBACjD,IAAI;gBACN;gBAEA,MAAM,SAAS,MAAM,YAAY;gBACjC,MAAM,wBAAwB,KAAK,GAAG,KAAK;gBAE3C,IAAI,OAAO,UAAU,EAAE;oBACrB,mBAAmB,OAAO,UAAU;gBACtC;gBAEA,MAAM,gBAAgB;oBACpB,SAAS,OAAO,OAAO;oBACvB,SAAS,OAAO,aAAa;oBAC7B,OAAO,OAAO,KAAK;oBACnB,SAAS;wBACP,YAAY,OAAO,UAAU;wBAC7B,YAAY,OAAO,UAAU;wBAC7B,OAAO,OAAO,KAAK;wBACnB,gBAAgB;wBAChB,eAAe,QAAQ,MAAM;wBAC7B,cAAc,QAAQ,KAAK;wBAC3B,YAAY,CAAC,CAAC,CAAC,gBAAgB,eAAc;wBAC7C,eAAe,OAAO,aAAa;wBACnC,WAAW,OAAO,SAAS;oBAC7B;gBACF;gBAEA,OAAO,CAAC,MAAM,GAAG;gBACjB;gBAEA,WAAW;gBACX,IAAI,mBAAmB;oBACrB,kBAAkB,OAAO;gBAC3B;gBAEA,OAAO;gBACP,MAAM,WAAW,AAAC,YAAY,SAAS,MAAM,GAAI;gBACjD,MAAM,YAAY,KAAK,GAAG,KAAK;gBAC/B,MAAM,wBAAwB,YAAY;gBAE1C,IAAI,YAAY;oBACd,WAAW,UAAU,QAAQ,MAAM,EAAE;wBACnC;wBACA;wBACA;wBACA;wBACA,YAAY,CAAC,CAAC,CAAC,gBAAgB,eAAc;oBAC/C;gBACF;gBAEA,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,QAAQ,MAAM,CAAC,IAAI,EAAE,OAAO,OAAO,GAAG,OAAO,OAAO,gBAAgB,kBAAiB,YAAY,GAAG,EAAE,EAAE,OAAO,KAAK,IAAI,MAAM;YAEjJ,EAAE,OAAO,OAAO;gBACd,MAAM,wBAAwB,KAAK,GAAG,KAAK;gBAC3C,MAAM,gBAAgB;oBACpB,SAAS;oBACT,SAAS;oBACT,OAAO,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;oBACjE,SAAS;wBACP,gBAAgB;wBAChB,eAAe,QAAQ,MAAM;wBAC7B,cAAc,QAAQ,KAAK;wBAC3B,YAAY,CAAC,CAAC,CAAC,gBAAgB,cAAc;oBAC/C;gBACF;gBAEA,OAAO,CAAC,MAAM,GAAG;gBACjB;gBAEA,IAAI,mBAAmB;oBACrB,kBAAkB,OAAO;gBAC3B;gBAEA,QAAQ,KAAK,CAAC,CAAC,EAAE,EAAE,QAAQ,MAAM,CAAC,OAAO,CAAC,EAAE;YAC9C,SAAU;gBACR;YACF;QACF;QAEA,WAAW;QACX,MAAM,WAAW,SAAS,GAAG,CAAC,CAAC,SAAS,QAAU,eAAe,SAAS;QAC1E,MAAM,QAAQ,GAAG,CAAC;QAElB,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,oBAAoB;QACpB,MAAM,EAAE,eAAe,EAAE,GAAG;QAC5B,OAAO,MAAM,gBACX,UACA,OACA,YACA,mBACA,aACA,OACA;IAEJ;AACF", "debugId": null}}, {"offset": {"line": 888, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/lib/file-manager.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\n\n// 文件管理工具类\nexport class FileManager {\n  private static instance: FileManager;\n  private baseDir: string;\n\n  private constructor() {\n    this.baseDir = process.cwd();\n  }\n\n  public static getInstance(): FileManager {\n    if (!FileManager.instance) {\n      FileManager.instance = new FileManager();\n    }\n    return FileManager.instance;\n  }\n\n  // 确保目录存在\n  public ensureDir(dirPath: string): void {\n    if (!fs.existsSync(dirPath)) {\n      fs.mkdirSync(dirPath, { recursive: true });\n    }\n  }\n\n  // 获取novels目录路径\n  public getNovelsDir(): string {\n    return path.join(this.baseDir, '..', 'novels');\n  }\n\n  // 获取chapters目录路径\n  public getChaptersDir(): string {\n    return path.join(this.baseDir, '..', 'chapters');\n  }\n\n  // 获取数据目录路径\n  public getDataDir(): string {\n    const dataDir = path.join(this.baseDir, 'data');\n    this.ensureDir(dataDir);\n    return dataDir;\n  }\n\n  // 获取改写结果目录路径\n  public getRewrittenDir(): string {\n    const rewrittenDir = path.join(this.getDataDir(), 'rewritten');\n    this.ensureDir(rewrittenDir);\n    return rewrittenDir;\n  }\n\n  // 获取特定小说的改写结果目录\n  public getNovelRewrittenDir(novelTitle: string): string {\n    const novelDir = path.join(this.getRewrittenDir(), this.sanitizeFilename(novelTitle));\n    this.ensureDir(novelDir);\n    return novelDir;\n  }\n\n  // 获取完成小说目录路径\n  public getDoneNovelsDir(): string {\n    const doneNovelsDir = path.join(this.baseDir, '..', 'done-novels');\n    this.ensureDir(doneNovelsDir);\n    return doneNovelsDir;\n  }\n\n  // 获取特定小说的章节目录\n  public getNovelChaptersDir(novelTitle: string): string {\n    const chaptersDir = this.getChaptersDir();\n    this.ensureDir(chaptersDir);\n    const novelDir = path.join(chaptersDir, this.sanitizeFilename(novelTitle));\n    this.ensureDir(novelDir);\n    return novelDir;\n  }\n\n  // 清理文件名中的非法字符\n  public sanitizeFilename(filename: string): string {\n    return filename.replace(/[<>:\"/\\\\|?*]/g, '_').trim();\n  }\n\n  // 读取文件内容\n  public readFile(filePath: string): string {\n    try {\n      return fs.readFileSync(filePath, 'utf-8');\n    } catch (error) {\n      console.error(`读取文件失败: ${filePath}`, error);\n      throw error;\n    }\n  }\n\n  // 写入文件内容\n  public writeFile(filePath: string, content: string): void {\n    try {\n      const dir = path.dirname(filePath);\n      this.ensureDir(dir);\n      fs.writeFileSync(filePath, content, 'utf-8');\n    } catch (error) {\n      console.error(`写入文件失败: ${filePath}`, error);\n      throw error;\n    }\n  }\n\n  // 检查文件是否存在\n  public fileExists(filePath: string): boolean {\n    return fs.existsSync(filePath);\n  }\n\n  // 检查目录是否存在\n  public directoryExists(dirPath: string): boolean {\n    try {\n      return fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory();\n    } catch (error) {\n      return false;\n    }\n  }\n\n  // 获取目录中的所有文件\n  public listFiles(dirPath: string, extensions?: string[]): string[] {\n    try {\n      if (!fs.existsSync(dirPath)) {\n        return [];\n      }\n\n      const files = fs.readdirSync(dirPath);\n\n      if (extensions) {\n        return files.filter(file => {\n          const ext = path.extname(file).toLowerCase();\n          return extensions.includes(ext);\n        });\n      }\n\n      return files;\n    } catch (error) {\n      console.error(`读取目录失败: ${dirPath}`, error);\n      return [];\n    }\n  }\n\n  // 获取文件信息\n  public getFileStats(filePath: string): fs.Stats | null {\n    try {\n      return fs.statSync(filePath);\n    } catch (error) {\n      console.error(`获取文件信息失败: ${filePath}`, error);\n      return null;\n    }\n  }\n\n  // 删除文件\n  public deleteFile(filePath: string): boolean {\n    try {\n      if (fs.existsSync(filePath)) {\n        fs.unlinkSync(filePath);\n        return true;\n      }\n      return false;\n    } catch (error) {\n      console.error(`删除文件失败: ${filePath}`, error);\n      return false;\n    }\n  }\n\n  // 删除目录\n  public deleteDir(dirPath: string): boolean {\n    try {\n      if (fs.existsSync(dirPath)) {\n        fs.rmSync(dirPath, { recursive: true, force: true });\n        return true;\n      }\n      return false;\n    } catch (error) {\n      console.error(`删除目录失败: ${dirPath}`, error);\n      return false;\n    }\n  }\n\n  // 复制文件\n  public copyFile(srcPath: string, destPath: string): boolean {\n    try {\n      const destDir = path.dirname(destPath);\n      this.ensureDir(destDir);\n      fs.copyFileSync(srcPath, destPath);\n      return true;\n    } catch (error) {\n      console.error(`复制文件失败: ${srcPath} -> ${destPath}`, error);\n      return false;\n    }\n  }\n\n  // 移动文件\n  public moveFile(srcPath: string, destPath: string): boolean {\n    try {\n      const destDir = path.dirname(destPath);\n      this.ensureDir(destDir);\n      fs.renameSync(srcPath, destPath);\n      return true;\n    } catch (error) {\n      console.error(`移动文件失败: ${srcPath} -> ${destPath}`, error);\n      return false;\n    }\n  }\n\n  // 获取目录大小\n  public getDirSize(dirPath: string): number {\n    let totalSize = 0;\n\n    try {\n      if (!fs.existsSync(dirPath)) {\n        return 0;\n      }\n\n      const files = fs.readdirSync(dirPath);\n\n      for (const file of files) {\n        const filePath = path.join(dirPath, file);\n        const stats = fs.statSync(filePath);\n\n        if (stats.isDirectory()) {\n          totalSize += this.getDirSize(filePath);\n        } else {\n          totalSize += stats.size;\n        }\n      }\n    } catch (error) {\n      console.error(`计算目录大小失败: ${dirPath}`, error);\n    }\n\n    return totalSize;\n  }\n\n  // 格式化文件大小\n  public formatFileSize(bytes: number): string {\n    if (bytes === 0) return '0 B';\n\n    const k = 1024;\n    const sizes = ['B', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  // 创建备份\n  public createBackup(filePath: string): string | null {\n    try {\n      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');\n      const ext = path.extname(filePath);\n      const baseName = path.basename(filePath, ext);\n      const dir = path.dirname(filePath);\n\n      const backupPath = path.join(dir, `${baseName}_backup_${timestamp}${ext}`);\n\n      if (this.copyFile(filePath, backupPath)) {\n        return backupPath;\n      }\n\n      return null;\n    } catch (error) {\n      console.error(`创建备份失败: ${filePath}`, error);\n      return null;\n    }\n  }\n\n  // 合并改写的章节为一个完整的小说文件\n  public mergeRewrittenChapters(novelTitle: string): { success: boolean; filePath?: string; error?: string } {\n    try {\n      const rewrittenDir = this.getNovelRewrittenDir(novelTitle);\n      const doneNovelsDir = this.getDoneNovelsDir();\n\n      // 获取所有改写的章节文件\n      const chapterFiles = this.listFiles(rewrittenDir, ['.txt'])\n        .filter(file => file.startsWith('chapter_') && file.includes('_rewritten'))\n        .sort((a, b) => {\n          // 提取章节号进行排序\n          const aNum = parseInt(a.match(/chapter_(\\d+)/)?.[1] || '0');\n          const bNum = parseInt(b.match(/chapter_(\\d+)/)?.[1] || '0');\n          return aNum - bNum;\n        });\n\n      if (chapterFiles.length === 0) {\n        return { success: false, error: '没有找到改写的章节文件' };\n      }\n\n      // 读取并合并所有章节\n      let mergedContent = '';\n      for (const chapterFile of chapterFiles) {\n        const chapterPath = path.join(rewrittenDir, chapterFile);\n        const chapterContent = this.readFile(chapterPath);\n        mergedContent += chapterContent + '\\n\\n';\n      }\n\n      // 生成合并后的文件名\n      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);\n      const mergedFileName = `${this.sanitizeFilename(novelTitle)}_merged_${timestamp}.txt`;\n      const mergedFilePath = path.join(doneNovelsDir, mergedFileName);\n\n      // 写入合并后的文件\n      this.writeFile(mergedFilePath, mergedContent.trim());\n\n      return {\n        success: true,\n        filePath: mergedFilePath\n      };\n    } catch (error) {\n      console.error(`合并章节失败: ${novelTitle}`, error);\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : '未知错误'\n      };\n    }\n  }\n\n  // 清理旧备份文件\n  public cleanupBackups(dirPath: string, maxBackups: number = 5): void {\n    try {\n      const files = this.listFiles(dirPath);\n      const backupFiles = files\n        .filter(file => file.includes('_backup_'))\n        .map(file => ({\n          name: file,\n          path: path.join(dirPath, file),\n          stats: this.getFileStats(path.join(dirPath, file))\n        }))\n        .filter(item => item.stats !== null)\n        .sort((a, b) => b.stats!.mtime.getTime() - a.stats!.mtime.getTime());\n\n      // 删除超出数量限制的备份文件\n      if (backupFiles.length > maxBackups) {\n        const filesToDelete = backupFiles.slice(maxBackups);\n        for (const file of filesToDelete) {\n          this.deleteFile(file.path);\n        }\n      }\n    } catch (error) {\n      console.error(`清理备份文件失败: ${dirPath}`, error);\n    }\n  }\n}\n\n// 导出单例实例\nexport const fileManager = FileManager.getInstance();\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAGO,MAAM;IACX,OAAe,SAAsB;IAC7B,QAAgB;IAExB,aAAsB;QACpB,IAAI,CAAC,OAAO,GAAG,QAAQ,GAAG;IAC5B;IAEA,OAAc,cAA2B;QACvC,IAAI,CAAC,YAAY,QAAQ,EAAE;YACzB,YAAY,QAAQ,GAAG,IAAI;QAC7B;QACA,OAAO,YAAY,QAAQ;IAC7B;IAEA,SAAS;IACF,UAAU,OAAe,EAAQ;QACtC,IAAI,CAAC,wGAAE,CAAC,UAAU,CAAC,UAAU;YAC3B,wGAAE,CAAC,SAAS,CAAC,SAAS;gBAAE,WAAW;YAAK;QAC1C;IACF;IAEA,eAAe;IACR,eAAuB;QAC5B,OAAO,4GAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM;IACvC;IAEA,iBAAiB;IACV,iBAAyB;QAC9B,OAAO,4GAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM;IACvC;IAEA,WAAW;IACJ,aAAqB;QAC1B,MAAM,UAAU,4GAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;QACxC,IAAI,CAAC,SAAS,CAAC;QACf,OAAO;IACT;IAEA,aAAa;IACN,kBAA0B;QAC/B,MAAM,eAAe,4GAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI;QAClD,IAAI,CAAC,SAAS,CAAC;QACf,OAAO;IACT;IAEA,gBAAgB;IACT,qBAAqB,UAAkB,EAAU;QACtD,MAAM,WAAW,4GAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,gBAAgB,CAAC;QACzE,IAAI,CAAC,SAAS,CAAC;QACf,OAAO;IACT;IAEA,aAAa;IACN,mBAA2B;QAChC,MAAM,gBAAgB,4GAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM;QACpD,IAAI,CAAC,SAAS,CAAC;QACf,OAAO;IACT;IAEA,cAAc;IACP,oBAAoB,UAAkB,EAAU;QACrD,MAAM,cAAc,IAAI,CAAC,cAAc;QACvC,IAAI,CAAC,SAAS,CAAC;QACf,MAAM,WAAW,4GAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,gBAAgB,CAAC;QAC9D,IAAI,CAAC,SAAS,CAAC;QACf,OAAO;IACT;IAEA,cAAc;IACP,iBAAiB,QAAgB,EAAU;QAChD,OAAO,SAAS,OAAO,CAAC,iBAAiB,KAAK,IAAI;IACpD;IAEA,SAAS;IACF,SAAS,QAAgB,EAAU;QACxC,IAAI;YACF,OAAO,wGAAE,CAAC,YAAY,CAAC,UAAU;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE;YACrC,MAAM;QACR;IACF;IAEA,SAAS;IACF,UAAU,QAAgB,EAAE,OAAe,EAAQ;QACxD,IAAI;YACF,MAAM,MAAM,4GAAI,CAAC,OAAO,CAAC;YACzB,IAAI,CAAC,SAAS,CAAC;YACf,wGAAE,CAAC,aAAa,CAAC,UAAU,SAAS;QACtC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE;YACrC,MAAM;QACR;IACF;IAEA,WAAW;IACJ,WAAW,QAAgB,EAAW;QAC3C,OAAO,wGAAE,CAAC,UAAU,CAAC;IACvB;IAEA,WAAW;IACJ,gBAAgB,OAAe,EAAW;QAC/C,IAAI;YACF,OAAO,wGAAE,CAAC,UAAU,CAAC,YAAY,wGAAE,CAAC,QAAQ,CAAC,SAAS,WAAW;QACnE,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,aAAa;IACN,UAAU,OAAe,EAAE,UAAqB,EAAY;QACjE,IAAI;YACF,IAAI,CAAC,wGAAE,CAAC,UAAU,CAAC,UAAU;gBAC3B,OAAO,EAAE;YACX;YAEA,MAAM,QAAQ,wGAAE,CAAC,WAAW,CAAC;YAE7B,IAAI,YAAY;gBACd,OAAO,MAAM,MAAM,CAAC,CAAA;oBAClB,MAAM,MAAM,4GAAI,CAAC,OAAO,CAAC,MAAM,WAAW;oBAC1C,OAAO,WAAW,QAAQ,CAAC;gBAC7B;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE;YACpC,OAAO,EAAE;QACX;IACF;IAEA,SAAS;IACF,aAAa,QAAgB,EAAmB;QACrD,IAAI;YACF,OAAO,wGAAE,CAAC,QAAQ,CAAC;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,UAAU,EAAE,UAAU,EAAE;YACvC,OAAO;QACT;IACF;IAEA,OAAO;IACA,WAAW,QAAgB,EAAW;QAC3C,IAAI;YACF,IAAI,wGAAE,CAAC,UAAU,CAAC,WAAW;gBAC3B,wGAAE,CAAC,UAAU,CAAC;gBACd,OAAO;YACT;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE;YACrC,OAAO;QACT;IACF;IAEA,OAAO;IACA,UAAU,OAAe,EAAW;QACzC,IAAI;YACF,IAAI,wGAAE,CAAC,UAAU,CAAC,UAAU;gBAC1B,wGAAE,CAAC,MAAM,CAAC,SAAS;oBAAE,WAAW;oBAAM,OAAO;gBAAK;gBAClD,OAAO;YACT;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE;YACpC,OAAO;QACT;IACF;IAEA,OAAO;IACA,SAAS,OAAe,EAAE,QAAgB,EAAW;QAC1D,IAAI;YACF,MAAM,UAAU,4GAAI,CAAC,OAAO,CAAC;YAC7B,IAAI,CAAC,SAAS,CAAC;YACf,wGAAE,CAAC,YAAY,CAAC,SAAS;YACzB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,QAAQ,IAAI,EAAE,UAAU,EAAE;YACnD,OAAO;QACT;IACF;IAEA,OAAO;IACA,SAAS,OAAe,EAAE,QAAgB,EAAW;QAC1D,IAAI;YACF,MAAM,UAAU,4GAAI,CAAC,OAAO,CAAC;YAC7B,IAAI,CAAC,SAAS,CAAC;YACf,wGAAE,CAAC,UAAU,CAAC,SAAS;YACvB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,QAAQ,IAAI,EAAE,UAAU,EAAE;YACnD,OAAO;QACT;IACF;IAEA,SAAS;IACF,WAAW,OAAe,EAAU;QACzC,IAAI,YAAY;QAEhB,IAAI;YACF,IAAI,CAAC,wGAAE,CAAC,UAAU,CAAC,UAAU;gBAC3B,OAAO;YACT;YAEA,MAAM,QAAQ,wGAAE,CAAC,WAAW,CAAC;YAE7B,KAAK,MAAM,QAAQ,MAAO;gBACxB,MAAM,WAAW,4GAAI,CAAC,IAAI,CAAC,SAAS;gBACpC,MAAM,QAAQ,wGAAE,CAAC,QAAQ,CAAC;gBAE1B,IAAI,MAAM,WAAW,IAAI;oBACvB,aAAa,IAAI,CAAC,UAAU,CAAC;gBAC/B,OAAO;oBACL,aAAa,MAAM,IAAI;gBACzB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,UAAU,EAAE,SAAS,EAAE;QACxC;QAEA,OAAO;IACT;IAEA,UAAU;IACH,eAAe,KAAa,EAAU;QAC3C,IAAI,UAAU,GAAG,OAAO;QAExB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAK;YAAM;YAAM;SAAK;QACrC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,OAAO;IACA,aAAa,QAAgB,EAAiB;QACnD,IAAI;YACF,MAAM,YAAY,IAAI,OAAO,WAAW,GAAG,OAAO,CAAC,SAAS;YAC5D,MAAM,MAAM,4GAAI,CAAC,OAAO,CAAC;YACzB,MAAM,WAAW,4GAAI,CAAC,QAAQ,CAAC,UAAU;YACzC,MAAM,MAAM,4GAAI,CAAC,OAAO,CAAC;YAEzB,MAAM,aAAa,4GAAI,CAAC,IAAI,CAAC,KAAK,GAAG,SAAS,QAAQ,EAAE,YAAY,KAAK;YAEzE,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,aAAa;gBACvC,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE;YACrC,OAAO;QACT;IACF;IAEA,oBAAoB;IACb,uBAAuB,UAAkB,EAA2D;QACzG,IAAI;YACF,MAAM,eAAe,IAAI,CAAC,oBAAoB,CAAC;YAC/C,MAAM,gBAAgB,IAAI,CAAC,gBAAgB;YAE3C,cAAc;YACd,MAAM,eAAe,IAAI,CAAC,SAAS,CAAC,cAAc;gBAAC;aAAO,EACvD,MAAM,CAAC,CAAA,OAAQ,KAAK,UAAU,CAAC,eAAe,KAAK,QAAQ,CAAC,eAC5D,IAAI,CAAC,CAAC,GAAG;gBACR,YAAY;gBACZ,MAAM,OAAO,SAAS,EAAE,KAAK,CAAC,kBAAkB,CAAC,EAAE,IAAI;gBACvD,MAAM,OAAO,SAAS,EAAE,KAAK,CAAC,kBAAkB,CAAC,EAAE,IAAI;gBACvD,OAAO,OAAO;YAChB;YAEF,IAAI,aAAa,MAAM,KAAK,GAAG;gBAC7B,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAc;YAChD;YAEA,YAAY;YACZ,IAAI,gBAAgB;YACpB,KAAK,MAAM,eAAe,aAAc;gBACtC,MAAM,cAAc,4GAAI,CAAC,IAAI,CAAC,cAAc;gBAC5C,MAAM,iBAAiB,IAAI,CAAC,QAAQ,CAAC;gBACrC,iBAAiB,iBAAiB;YACpC;YAEA,YAAY;YACZ,MAAM,YAAY,IAAI,OAAO,WAAW,GAAG,OAAO,CAAC,SAAS,KAAK,SAAS,CAAC,GAAG;YAC9E,MAAM,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,QAAQ,EAAE,UAAU,IAAI,CAAC;YACrF,MAAM,iBAAiB,4GAAI,CAAC,IAAI,CAAC,eAAe;YAEhD,WAAW;YACX,IAAI,CAAC,SAAS,CAAC,gBAAgB,cAAc,IAAI;YAEjD,OAAO;gBACL,SAAS;gBACT,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,YAAY,EAAE;YACvC,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,UAAU;IACH,eAAe,OAAe,EAAE,aAAqB,CAAC,EAAQ;QACnE,IAAI;YACF,MAAM,QAAQ,IAAI,CAAC,SAAS,CAAC;YAC7B,MAAM,cAAc,MACjB,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,CAAC,aAC7B,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACZ,MAAM;oBACN,MAAM,4GAAI,CAAC,IAAI,CAAC,SAAS;oBACzB,OAAO,IAAI,CAAC,YAAY,CAAC,4GAAI,CAAC,IAAI,CAAC,SAAS;gBAC9C,CAAC,GACA,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK,MAC9B,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,CAAE,KAAK,CAAC,OAAO,KAAK,EAAE,KAAK,CAAE,KAAK,CAAC,OAAO;YAEnE,gBAAgB;YAChB,IAAI,YAAY,MAAM,GAAG,YAAY;gBACnC,MAAM,gBAAgB,YAAY,KAAK,CAAC;gBACxC,KAAK,MAAM,QAAQ,cAAe;oBAChC,IAAI,CAAC,UAAU,CAAC,KAAK,IAAI;gBAC3B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,UAAU,EAAE,SAAS,EAAE;QACxC;IACF;AACF;AAGO,MAAM,cAAc,YAAY,WAAW", "debugId": null}}, {"offset": {"line": 1198, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/app/api/rewrite/retry/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { chapterDb, jobDb, novelDb } from '@/lib/database';\nimport { rewriteTextWithContextServer } from '@/lib/context-utils';\nimport { fileManager } from '@/lib/file-manager';\nimport path from 'path';\n\n// POST - 重试失败的章节\nexport async function POST(request: NextRequest) {\n  try {\n    const { jobId, chapterNumbers, rules, model } = await request.json();\n\n    if (!jobId || !chapterNumbers || !Array.isArray(chapterNumbers) || !rules) {\n      return NextResponse.json(\n        { success: false, error: '参数不完整' },\n        { status: 400 }\n      );\n    }\n\n    // 获取任务信息\n    const job = jobDb.getById(jobId);\n    if (!job) {\n      return NextResponse.json(\n        { success: false, error: '任务不存在' },\n        { status: 404 }\n      );\n    }\n\n    // 获取小说信息\n    const novel = novelDb.getById(job.novelId);\n    if (!novel) {\n      return NextResponse.json(\n        { success: false, error: '小说不存在' },\n        { status: 404 }\n      );\n    }\n\n    // 获取要重试的章节\n    const allChapters = chapterDb.getByNovelId(job.novelId);\n    const chaptersToRetry = allChapters.filter(chapter =>\n      chapterNumbers.includes(chapter.chapterNumber)\n    );\n\n    if (chaptersToRetry.length === 0) {\n      return NextResponse.json(\n        { success: false, error: '没有找到指定的章节' },\n        { status: 404 }\n      );\n    }\n\n    // 更新任务状态\n    jobDb.update(jobId, {\n      status: 'processing',\n      details: {\n        ...job.details,\n        retryInProgress: true,\n        retryChapters: chapterNumbers,\n      }\n    });\n\n    // 异步执行重试\n    executeRetryJob(jobId, chaptersToRetry, rules, novel.title, model || 'gemini-2.5-flash-lite');\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        jobId,\n        retryChaptersCount: chaptersToRetry.length,\n        message: '重试任务已创建，正在处理中...',\n      },\n    });\n\n  } catch (error) {\n    console.error('创建重试任务失败:', error);\n    return NextResponse.json(\n      { success: false, error: '创建重试任务失败' },\n      { status: 500 }\n    );\n  }\n}\n\n// 异步执行重试任务\nasync function executeRetryJob(\n  jobId: string,\n  chapters: Array<{\n    id: string;\n    novelId: string;\n    chapterNumber: number;\n    title: string;\n    content: string;\n    filename: string;\n    createdAt: string;\n  }>,\n  rules: string,\n  novelTitle: string,\n  model: string = 'gemini-2.5-flash-lite'\n) {\n  const startTime = Date.now();\n  const outputDir = fileManager.getNovelRewrittenDir(novelTitle);\n  let successCount = 0;\n  const retryResults: Array<{\n    chapterNumber: number;\n    chapterTitle: string;\n    success: boolean;\n    error?: string;\n    apiKeyUsed?: string;\n    tokensUsed?: number;\n    processingTime?: number;\n  }> = [];\n\n  try {\n    // 串行处理重试章节，避免API限制\n    for (const chapter of chapters) {\n      console.log(`正在重试第 ${chapter.chapterNumber} 章: ${chapter.title}`);\n\n      const chapterStartTime = Date.now();\n\n      try {\n        // 等待一段时间再处理下一个章节\n        if (retryResults.length > 0) {\n          await new Promise(resolve => setTimeout(resolve, 3000));\n        }\n\n        const result = await rewriteTextWithContextServer(\n          chapter.novelId,\n          chapter.chapterNumber,\n          chapter.content,\n          rules,\n          chapter.title,\n          model\n        );\n\n        const chapterProcessingTime = Date.now() - chapterStartTime;\n\n        if (result.success) {\n          // 保存成功重试的章节\n          const filename = `chapter_${chapter.chapterNumber}_rewritten.txt`;\n          const filePath = path.join(outputDir, filename);\n          fileManager.writeFile(filePath, result.rewrittenText);\n          successCount++;\n        }\n\n        retryResults.push({\n          chapterNumber: chapter.chapterNumber,\n          chapterTitle: chapter.title,\n          success: result.success,\n          error: result.error,\n          apiKeyUsed: result.apiKeyUsed,\n          tokensUsed: result.tokensUsed,\n          processingTime: chapterProcessingTime,\n        });\n\n        console.log(`第 ${chapter.chapterNumber} 章重试${result.success ? '成功' : '失败'}: ${result.error || '完成'}`);\n\n      } catch (error) {\n        const chapterProcessingTime = Date.now() - chapterStartTime;\n        retryResults.push({\n          chapterNumber: chapter.chapterNumber,\n          chapterTitle: chapter.title,\n          success: false,\n          error: `重试异常: ${error instanceof Error ? error.message : '未知错误'}`,\n          processingTime: chapterProcessingTime,\n        });\n        console.error(`第 ${chapter.chapterNumber} 章重试异常:`, error);\n      }\n    }\n\n    // 更新任务状态\n    const currentJob = jobDb.getById(jobId);\n    if (currentJob?.details) {\n      const updatedChapterResults = [...(currentJob.details.chapterResults || [])];\n\n      // 更新重试章节的结果\n      retryResults.forEach(retryResult => {\n        const existingIndex = updatedChapterResults.findIndex(\n          r => r && r.chapterNumber === retryResult.chapterNumber\n        );\n\n        if (existingIndex >= 0) {\n          updatedChapterResults[existingIndex] = {\n            ...updatedChapterResults[existingIndex],\n            ...retryResult,\n            completedAt: new Date().toISOString(),\n            isRetried: true,\n          };\n        }\n      });\n\n      const totalProcessingTime = Date.now() - startTime;\n      const newCompletedCount = updatedChapterResults.filter(r => r && r.success).length;\n      const newFailedCount = updatedChapterResults.filter(r => r && !r.success).length;\n\n      jobDb.update(jobId, {\n        status: 'completed',\n        progress: 100,\n        result: `重试完成: ${successCount}/${chapters.length} 章节成功，总计 ${newCompletedCount}/${currentJob.details.totalChapters} 章节完成`,\n        details: {\n          ...currentJob.details,\n          completedChapters: newCompletedCount,\n          failedChapters: newFailedCount,\n          chapterResults: updatedChapterResults,\n          retryInProgress: false,\n          retryResults,\n          lastRetryTime: new Date().toISOString(),\n          retryProcessingTime: totalProcessingTime,\n        }\n      });\n    }\n\n    // 保存重试结果摘要\n    const retrySummaryPath = path.join(outputDir, `retry_summary_${Date.now()}.json`);\n    const retrySummaryData = JSON.stringify({\n      jobId,\n      novelTitle,\n      retryChapters: chapters.map(c => ({ number: c.chapterNumber, title: c.title })),\n      successCount,\n      failedCount: chapters.length - successCount,\n      totalProcessingTime: Date.now() - startTime,\n      results: retryResults,\n      completedAt: new Date().toISOString(),\n      model,\n    }, null, 2);\n    fileManager.writeFile(retrySummaryPath, retrySummaryData);\n\n  } catch (error) {\n    console.error('执行重试任务失败:', error);\n    jobDb.update(jobId, {\n      status: 'failed',\n      result: `重试失败: ${error instanceof Error ? error.message : '未知错误'}`,\n      details: {\n        ...jobDb.getById(jobId)?.details,\n        retryInProgress: false,\n        retryError: error instanceof Error ? error.message : '未知错误',\n      }\n    });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,IAAI;QAElE,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,MAAM,OAAO,CAAC,mBAAmB,CAAC,OAAO;YACzE,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAQ,GACjC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,MAAM,iIAAK,CAAC,OAAO,CAAC;QAC1B,IAAI,CAAC,KAAK;YACR,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAQ,GACjC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,QAAQ,mIAAO,CAAC,OAAO,CAAC,IAAI,OAAO;QACzC,IAAI,CAAC,OAAO;YACV,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAQ,GACjC;gBAAE,QAAQ;YAAI;QAElB;QAEA,WAAW;QACX,MAAM,cAAc,qIAAS,CAAC,YAAY,CAAC,IAAI,OAAO;QACtD,MAAM,kBAAkB,YAAY,MAAM,CAAC,CAAA,UACzC,eAAe,QAAQ,CAAC,QAAQ,aAAa;QAG/C,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAY,GACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,iIAAK,CAAC,MAAM,CAAC,OAAO;YAClB,QAAQ;YACR,SAAS;gBACP,GAAG,IAAI,OAAO;gBACd,iBAAiB;gBACjB,eAAe;YACjB;QACF;QAEA,SAAS;QACT,gBAAgB,OAAO,iBAAiB,OAAO,MAAM,KAAK,EAAE,SAAS;QAErE,OAAO,gJAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ;gBACA,oBAAoB,gBAAgB,MAAM;gBAC1C,SAAS;YACX;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAW,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,WAAW;AACX,eAAe,gBACb,KAAa,EACb,QAQE,EACF,KAAa,EACb,UAAkB,EAClB,QAAgB,uBAAuB;IAEvC,MAAM,YAAY,KAAK,GAAG;IAC1B,MAAM,YAAY,8IAAW,CAAC,oBAAoB,CAAC;IACnD,IAAI,eAAe;IACnB,MAAM,eAQD,EAAE;IAEP,IAAI;QACF,mBAAmB;QACnB,KAAK,MAAM,WAAW,SAAU;YAC9B,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,QAAQ,aAAa,CAAC,IAAI,EAAE,QAAQ,KAAK,EAAE;YAEhE,MAAM,mBAAmB,KAAK,GAAG;YAEjC,IAAI;gBACF,iBAAiB;gBACjB,IAAI,aAAa,MAAM,GAAG,GAAG;oBAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACnD;gBAEA,MAAM,SAAS,MAAM,IAAA,gLAA4B,EAC/C,QAAQ,OAAO,EACf,QAAQ,aAAa,EACrB,QAAQ,OAAO,EACf,OACA,QAAQ,KAAK,EACb;gBAGF,MAAM,wBAAwB,KAAK,GAAG,KAAK;gBAE3C,IAAI,OAAO,OAAO,EAAE;oBAClB,YAAY;oBACZ,MAAM,WAAW,CAAC,QAAQ,EAAE,QAAQ,aAAa,CAAC,cAAc,CAAC;oBACjE,MAAM,WAAW,4GAAI,CAAC,IAAI,CAAC,WAAW;oBACtC,8IAAW,CAAC,SAAS,CAAC,UAAU,OAAO,aAAa;oBACpD;gBACF;gBAEA,aAAa,IAAI,CAAC;oBAChB,eAAe,QAAQ,aAAa;oBACpC,cAAc,QAAQ,KAAK;oBAC3B,SAAS,OAAO,OAAO;oBACvB,OAAO,OAAO,KAAK;oBACnB,YAAY,OAAO,UAAU;oBAC7B,YAAY,OAAO,UAAU;oBAC7B,gBAAgB;gBAClB;gBAEA,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,QAAQ,aAAa,CAAC,IAAI,EAAE,OAAO,OAAO,GAAG,OAAO,KAAK,EAAE,EAAE,OAAO,KAAK,IAAI,MAAM;YAEtG,EAAE,OAAO,OAAO;gBACd,MAAM,wBAAwB,KAAK,GAAG,KAAK;gBAC3C,aAAa,IAAI,CAAC;oBAChB,eAAe,QAAQ,aAAa;oBACpC,cAAc,QAAQ,KAAK;oBAC3B,SAAS;oBACT,OAAO,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;oBACjE,gBAAgB;gBAClB;gBACA,QAAQ,KAAK,CAAC,CAAC,EAAE,EAAE,QAAQ,aAAa,CAAC,OAAO,CAAC,EAAE;YACrD;QACF;QAEA,SAAS;QACT,MAAM,aAAa,iIAAK,CAAC,OAAO,CAAC;QACjC,IAAI,YAAY,SAAS;YACvB,MAAM,wBAAwB;mBAAK,WAAW,OAAO,CAAC,cAAc,IAAI,EAAE;aAAE;YAE5E,YAAY;YACZ,aAAa,OAAO,CAAC,CAAA;gBACnB,MAAM,gBAAgB,sBAAsB,SAAS,CACnD,CAAA,IAAK,KAAK,EAAE,aAAa,KAAK,YAAY,aAAa;gBAGzD,IAAI,iBAAiB,GAAG;oBACtB,qBAAqB,CAAC,cAAc,GAAG;wBACrC,GAAG,qBAAqB,CAAC,cAAc;wBACvC,GAAG,WAAW;wBACd,aAAa,IAAI,OAAO,WAAW;wBACnC,WAAW;oBACb;gBACF;YACF;YAEA,MAAM,sBAAsB,KAAK,GAAG,KAAK;YACzC,MAAM,oBAAoB,sBAAsB,MAAM,CAAC,CAAA,IAAK,KAAK,EAAE,OAAO,EAAE,MAAM;YAClF,MAAM,iBAAiB,sBAAsB,MAAM,CAAC,CAAA,IAAK,KAAK,CAAC,EAAE,OAAO,EAAE,MAAM;YAEhF,iIAAK,CAAC,MAAM,CAAC,OAAO;gBAClB,QAAQ;gBACR,UAAU;gBACV,QAAQ,CAAC,MAAM,EAAE,aAAa,CAAC,EAAE,SAAS,MAAM,CAAC,SAAS,EAAE,kBAAkB,CAAC,EAAE,WAAW,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC;gBACxH,SAAS;oBACP,GAAG,WAAW,OAAO;oBACrB,mBAAmB;oBACnB,gBAAgB;oBAChB,gBAAgB;oBAChB,iBAAiB;oBACjB;oBACA,eAAe,IAAI,OAAO,WAAW;oBACrC,qBAAqB;gBACvB;YACF;QACF;QAEA,WAAW;QACX,MAAM,mBAAmB,4GAAI,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,KAAK,GAAG,GAAG,KAAK,CAAC;QAChF,MAAM,mBAAmB,KAAK,SAAS,CAAC;YACtC;YACA;YACA,eAAe,SAAS,GAAG,CAAC,CAAA,IAAK,CAAC;oBAAE,QAAQ,EAAE,aAAa;oBAAE,OAAO,EAAE,KAAK;gBAAC,CAAC;YAC7E;YACA,aAAa,SAAS,MAAM,GAAG;YAC/B,qBAAqB,KAAK,GAAG,KAAK;YAClC,SAAS;YACT,aAAa,IAAI,OAAO,WAAW;YACnC;QACF,GAAG,MAAM;QACT,8IAAW,CAAC,SAAS,CAAC,kBAAkB;IAE1C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,iIAAK,CAAC,MAAM,CAAC,OAAO;YAClB,QAAQ;YACR,QAAQ,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;YAClE,SAAS;gBACP,GAAG,iIAAK,CAAC,OAAO,CAAC,QAAQ,OAAO;gBAChC,iBAAiB;gBACjB,YAAY,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACvD;QACF;IACF;AACF", "debugId": null}}]}