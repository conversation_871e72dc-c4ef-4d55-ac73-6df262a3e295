[{"novelId": "mfxrf7myydaku63lib", "chapters": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 40, "completedChapters": 39, "failedChapters": 1, "totalTokensUsed": 191142, "totalProcessingTime": 69146, "averageTimePerChapter": 1728.65, "apiKeyStats": [{"name": "My First Project", "requestCount": 7, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "<PERSON><PERSON><PERSON>", "requestCount": 10, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "Generative Language Client", "requestCount": 8, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "In The Novel", "requestCount": 9, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "chat", "requestCount": 6, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}], "chapterResults": [{"chapterNumber": 1, "chapterTitle": "第一章 皇城的鬼", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4032, "processingTime": 7446}, {"chapterNumber": 2, "chapterTitle": "第二章 醒来的少年", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4609, "processingTime": 6326}, {"chapterNumber": 3, "chapterTitle": "第三章 遇见一个自己", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 3882, "processingTime": 7032}, {"chapterNumber": 4, "chapterTitle": "第四章 跪在殿前的少女", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 3950, "processingTime": 8050}, {"chapterNumber": 5, "chapterTitle": "第五章 雀鬼", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4162, "processingTime": 4691}, {"chapterNumber": 6, "chapterTitle": "第六章 小殿下", "success": true, "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4874, "processingTime": 6239}, {"chapterNumber": 7, "chapterTitle": "第七章 三更", "success": true, "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4235, "processingTime": 6619}, {"chapterNumber": 8, "chapterTitle": "第八章 榕树与日落", "success": true, "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4738, "processingTime": 6776}, {"chapterNumber": 9, "chapterTitle": "第九章 刀剑入夜", "success": true, "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3800, "processingTime": 2921}, {"chapterNumber": 10, "chapterTitle": "第十章 一纸空梦为谁书", "success": true, "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 6235, "processingTime": 13628}, {"chapterNumber": 11, "chapterTitle": "第十一章 殿下入井去，仙人乘轿来", "success": true, "apiKeyUsed": "Generative Language Client", "tokensUsed": 7160, "processingTime": 9380}, {"chapterNumber": 12, "chapterTitle": "第十二章 妖雀鸣城", "success": false, "error": "没有收到有效的响应内容", "apiKeyUsed": "Generative Language Client", "processingTime": 470}, {"chapterNumber": 13, "chapterTitle": "第十三章 仙子悬剑气如虹", "success": true, "apiKeyUsed": "In The Novel", "tokensUsed": 4459, "processingTime": 6134}, {"chapterNumber": 14, "chapterTitle": "第十四章 湖上狐影", "success": true, "apiKeyUsed": "In The Novel", "tokensUsed": 4626, "processingTime": 4113}, {"chapterNumber": 15, "chapterTitle": "第十五章 我为杀局，请君入瓮", "success": true, "apiKeyUsed": "In The Novel", "tokensUsed": 5125, "processingTime": 6199}, {"chapterNumber": 16, "chapterTitle": "第十六章 一个小道士的故事", "success": true, "apiKeyUsed": "chat", "tokensUsed": 5914, "processingTime": 9868}, {"chapterNumber": 17, "chapterTitle": "第十七章 皇宫下的背影", "success": true, "apiKeyUsed": "chat", "tokensUsed": 5994, "processingTime": 9012}, {"chapterNumber": 18, "chapterTitle": "第十八章 老狐一炬", "success": true, "apiKeyUsed": "chat", "tokensUsed": 4631, "processingTime": 1990}, {"chapterNumber": 19, "chapterTitle": "第十九章 一身白衣入城来", "success": true, "apiKeyUsed": "chat", "tokensUsed": 5396, "processingTime": 5015}, {"chapterNumber": 20, "chapterTitle": "第二十章 苏醒", "success": true, "apiKeyUsed": "chat", "tokensUsed": 3716, "processingTime": 5952}, {"chapterNumber": 21, "chapterTitle": "第二十一章 境界", "success": true, "apiKeyUsed": "chat", "tokensUsed": 3883, "processingTime": 7729}, {"chapterNumber": 22, "chapterTitle": "第二十二章 朱雀掠影焚天火", "success": true, "apiKeyUsed": "Generative Language Client", "tokensUsed": 4975, "processingTime": 3576}, {"chapterNumber": 23, "chapterTitle": "第二十三章 秋雨肃杀", "success": true, "apiKeyUsed": "Generative Language Client", "tokensUsed": 4016, "processingTime": 4355}, {"chapterNumber": 24, "chapterTitle": "第二十四章 狐影随形", "success": true, "apiKeyUsed": "Generative Language Client", "tokensUsed": 7510, "processingTime": 17769}, {"chapterNumber": 25, "chapterTitle": "未命名章节", "success": true, "apiKeyUsed": "Generative Language Client", "tokensUsed": 5052, "processingTime": 3106}, {"chapterNumber": 26, "chapterTitle": "第二十六章 夜幕降临之前", "success": true, "apiKeyUsed": "Generative Language Client", "tokensUsed": 4060, "processingTime": 3447}, {"chapterNumber": 27, "chapterTitle": "第二十七章 城楼之下谪仙人", "success": true, "apiKeyUsed": "Generative Language Client", "tokensUsed": 6045, "processingTime": 13957}, {"chapterNumber": 28, "chapterTitle": "第二十八章 城国之间，朱雀焚火", "success": true, "apiKeyUsed": "In The Novel", "tokensUsed": 5810, "processingTime": 8247}, {"chapterNumber": 29, "chapterTitle": "未命名章节", "success": true, "apiKeyUsed": "In The Novel", "tokensUsed": 4481, "processingTime": 3897}, {"chapterNumber": 30, "chapterTitle": "第三十章 风雪十六载，雨停烟花尽", "success": true, "apiKeyUsed": "In The Novel", "tokensUsed": 4210, "processingTime": 2526}, {"chapterNumber": 31, "chapterTitle": "第三十一章 就像是一场梦", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4077, "processingTime": 2816}, {"chapterNumber": 32, "chapterTitle": "未命名章节", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4731, "processingTime": 3286}, {"chapterNumber": 33, "chapterTitle": "第三十三章 妖种", "success": true, "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 5418, "processingTime": 10146}, {"chapterNumber": 34, "chapterTitle": "未命名章节", "success": true, "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 5533, "processingTime": 7089}, {"chapterNumber": 35, "chapterTitle": "第三十五章 仙剑来时", "success": true, "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4447, "processingTime": 6590}, {"chapterNumber": 36, "chapterTitle": "第三十六章 云至劫来", "success": true, "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 5107, "processingTime": 9967}, {"chapterNumber": 37, "chapterTitle": "第三十七章 心魔历劫", "success": true, "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4581, "processingTime": 8700}, {"chapterNumber": 38, "chapterTitle": "第三十八章 落雪之城，春寒料峭", "success": true, "apiKeyUsed": "In The Novel", "tokensUsed": 3828, "processingTime": 4932}, {"chapterNumber": 39, "chapterTitle": "第三十九章 白雪如梦，华裳如炬", "success": true, "apiKeyUsed": "In The Novel", "tokensUsed": 7159, "processingTime": 14566}, {"chapterNumber": 40, "chapterTitle": "第四十章 心魔领域里的小女孩", "success": true, "apiKeyUsed": "In The Novel", "tokensUsed": 4681, "processingTime": 5498}], "model": "gemini-2.5-flash", "concurrency": 5}, "id": "mfxwdhwfirw8kkyqr28", "createdAt": "2025-09-24T11:25:06.399Z", "updatedAt": "2025-09-24T11:26:15.550Z", "result": "成功改写 39/40 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfxyqop5xgwa9w2fo3", "chapters": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 40, "completedChapters": 39, "failedChapters": 1, "totalTokensUsed": 191424, "totalProcessingTime": 134206, "averageTimePerChapter": 3355.15, "apiKeyStats": [{"name": "My First Project", "requestCount": 17, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "<PERSON><PERSON><PERSON>", "requestCount": 17, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "Generative Language Client", "requestCount": 16, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "In The Novel", "requestCount": 14, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "chat", "requestCount": 16, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}], "chapterResults": [{"chapterNumber": 1, "chapterTitle": "第一章 皇城的鬼", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4213, "processingTime": 16444}, {"chapterNumber": 2, "chapterTitle": "第二章 醒来的少年", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4494, "processingTime": 12081}, {"chapterNumber": 3, "chapterTitle": "第三章 遇见一个自己", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4016, "processingTime": 21762}, {"chapterNumber": 4, "chapterTitle": "第四章 跪在殿前的少女", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3739, "processingTime": 7020}, {"chapterNumber": 5, "chapterTitle": "第五章 雀鬼", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 4469, "processingTime": 7695}, {"chapterNumber": 6, "chapterTitle": "第六章 小殿下", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 5297, "processingTime": 10480}, {"chapterNumber": 7, "chapterTitle": "第七章 三更", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4395, "processingTime": 7433}, {"chapterNumber": 8, "chapterTitle": "第八章 榕树与日落", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4653, "processingTime": 8649}, {"chapterNumber": 9, "chapterTitle": "第九章 刀剑入夜", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3799, "processingTime": 4267}, {"chapterNumber": 10, "chapterTitle": "第十章 一纸空梦为谁书", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 6247, "processingTime": 17413}, {"chapterNumber": 11, "chapterTitle": "第十一章 殿下入井去，仙人乘轿来", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 7310, "processingTime": 13840}, {"chapterNumber": 12, "chapterTitle": "第十二章 妖雀鸣城", "success": false, "error": "没有收到有效的响应内容", "apiKeyUsed": "Generative Language Client", "processingTime": 467}, {"chapterNumber": 13, "chapterTitle": "第十三章 仙子悬剑气如虹", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 4396, "processingTime": 9457}, {"chapterNumber": 14, "chapterTitle": "第十四章 湖上狐影", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 4377, "processingTime": 2944}, {"chapterNumber": 15, "chapterTitle": "第十五章 我为杀局，请君入瓮", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 4997, "processingTime": 7143}, {"chapterNumber": 16, "chapterTitle": "第十六章 一个小道士的故事", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 6002, "processingTime": 11194}, {"chapterNumber": 17, "chapterTitle": "第十七章 皇宫下的背影", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 6176, "processingTime": 11862}, {"chapterNumber": 18, "chapterTitle": "第十八章 老狐一炬", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4620, "processingTime": 2204}, {"chapterNumber": 19, "chapterTitle": "第十九章 一身白衣入城来", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 5365, "processingTime": 4778}, {"chapterNumber": 20, "chapterTitle": "第二十章 苏醒", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3997, "processingTime": 7196}, {"chapterNumber": 21, "chapterTitle": "第二十一章 境界", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 4890, "processingTime": 15694}, {"chapterNumber": 22, "chapterTitle": "第二十二章 朱雀掠影焚天火", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 5117, "processingTime": 6162}, {"chapterNumber": 23, "chapterTitle": "第二十三章 秋雨肃杀", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3882, "processingTime": 5155}, {"chapterNumber": 24, "chapterTitle": "第二十四章 狐影随形", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 7596, "processingTime": 20068}, {"chapterNumber": 25, "chapterTitle": "第二十五章 长街有雨，青衫接剑", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 5164, "processingTime": 4971}, {"chapterNumber": 26, "chapterTitle": "第二十六章 夜幕降临之前", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4513, "processingTime": 6512}, {"chapterNumber": 27, "chapterTitle": "第二十七章 城楼之下谪仙人", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 5014, "processingTime": 11257}, {"chapterNumber": 28, "chapterTitle": "第二十八章 城国之间，朱雀焚火", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 5825, "processingTime": 8998}, {"chapterNumber": 29, "chapterTitle": "第二十九章 天雷地火渐尾声", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 4443, "processingTime": 4815}, {"chapterNumber": 30, "chapterTitle": "第三十章 风雪十六载，雨停烟花尽", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 4753, "processingTime": 6453}, {"chapterNumber": 31, "chapterTitle": "第三十一章 就像是一场梦", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 4058, "processingTime": 3217}, {"chapterNumber": 32, "chapterTitle": "第三十二章 婚书", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 4662, "processingTime": 3338}, {"chapterNumber": 33, "chapterTitle": "第三十三章 妖种", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 5580, "processingTime": 10691}, {"chapterNumber": 34, "chapterTitle": "第三十四章 小院之战", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4802, "processingTime": 4730}, {"chapterNumber": 35, "chapterTitle": "第三十五章 仙剑来时", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4508, "processingTime": 8185}, {"chapterNumber": 36, "chapterTitle": "第三十六章 云至劫来", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4348, "processingTime": 6097}, {"chapterNumber": 37, "chapterTitle": "第三十七章 心魔历劫", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4542, "processingTime": 7487}, {"chapterNumber": 38, "chapterTitle": "第三十八章 落雪之城，春寒料峭", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3791, "processingTime": 4643}, {"chapterNumber": 39, "chapterTitle": "第三十九章 白雪如梦，华裳如炬", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 7195, "processingTime": 16501}, {"chapterNumber": 40, "chapterTitle": "第四十章 心魔领域里的小女孩", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 4179, "processingTime": 3982}], "model": "gemini-2.5-flash", "concurrency": 3}, "id": "mfy39elul1jvtiosbtc", "createdAt": "2025-09-24T14:37:52.818Z", "updatedAt": "2025-09-24T14:40:07.030Z", "result": "成功改写 39/40 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 40, "completedChapters": 39, "failedChapters": 1, "totalTokensUsed": 278860, "totalProcessingTime": 132555, "averageTimePerChapter": 3313.875, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 1, "chapterTitle": "第一章 皇城的鬼", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 5706, "processingTime": 5817}, {"chapterNumber": 2, "chapterTitle": "第二章 醒来的少年", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 7319, "processingTime": 8681}, {"chapterNumber": 3, "chapterTitle": "第三章 遇见一个自己", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 6219, "processingTime": 9569}, {"chapterNumber": 4, "chapterTitle": "第四章 跪在殿前的少女", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 6307, "processingTime": 8342}, {"chapterNumber": 5, "chapterTitle": "第五章 雀鬼", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 6399, "processingTime": 4202}, {"chapterNumber": 6, "chapterTitle": "第六章 小殿下", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 7475, "processingTime": 8828}, {"chapterNumber": 7, "chapterTitle": "第七章 三更", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 6298, "processingTime": 7083}, {"chapterNumber": 8, "chapterTitle": "第八章 榕树与日落", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 5988, "processingTime": 3649}, {"chapterNumber": 9, "chapterTitle": "第九章 刀剑入夜", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 5846, "processingTime": 4276}, {"chapterNumber": 10, "chapterTitle": "第十章 一纸空梦为谁书", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 7103, "processingTime": 5895}, {"chapterNumber": 11, "chapterTitle": "第十一章 殿下入井去，仙人乘轿来", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 9116, "processingTime": 8977}, {"chapterNumber": 12, "chapterTitle": "第十二章 妖雀鸣城", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 7418, "processingTime": 5897}, {"chapterNumber": 13, "chapterTitle": "第十三章 仙子悬剑气如虹", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 6588, "processingTime": 7203}, {"chapterNumber": 14, "chapterTitle": "第十四章 湖上狐影", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 6141, "processingTime": 2455}, {"chapterNumber": 15, "chapterTitle": "第十五章 我为杀局，请君入瓮", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 8472, "processingTime": 12446}, {"chapterNumber": 16, "chapterTitle": "第十六章 一个小道士的故事", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 8122, "processingTime": 12057}, {"chapterNumber": 17, "chapterTitle": "第十七章 皇宫下的背影", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 7727, "processingTime": 8502}, {"chapterNumber": 18, "chapterTitle": "第十八章 老狐一炬", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 6358, "processingTime": 2053}, {"chapterNumber": 19, "chapterTitle": "第十九章 一身白衣入城来", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 7370, "processingTime": 5271}, {"chapterNumber": 20, "chapterTitle": "第二十章 苏醒", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 6123, "processingTime": 9708}, {"chapterNumber": 21, "chapterTitle": "第二十一章 境界", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 6800, "processingTime": 12781}, {"chapterNumber": 22, "chapterTitle": "第二十二章 朱雀掠影焚天火", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 7734, "processingTime": 8991}, {"chapterNumber": 23, "chapterTitle": "第二十三章 秋雨肃杀", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 5857, "processingTime": 3175}, {"chapterNumber": 24, "chapterTitle": "第二十四章 狐影随形", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 9692, "processingTime": 16517}, {"chapterNumber": 25, "chapterTitle": "第二十五章 长街有雨，青衫接剑", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 8968, "processingTime": 13455}, {"chapterNumber": 26, "chapterTitle": "第二十六章 夜幕降临之前", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 6636, "processingTime": 6820}, {"chapterNumber": 27, "chapterTitle": "第二十七章 城楼之下谪仙人", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 8074, "processingTime": 12954}, {"chapterNumber": 28, "chapterTitle": "第二十八章 城国之间，朱雀焚火", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 7765, "processingTime": 9239}, {"chapterNumber": 29, "chapterTitle": "第二十九章 天雷地火渐尾声", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 6646, "processingTime": 7254}, {"chapterNumber": 30, "chapterTitle": "第三十章 风雪十六载，雨停烟花尽", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 6638, "processingTime": 6215}, {"chapterNumber": 31, "chapterTitle": "第三十一章 就像是一场梦", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 6818, "processingTime": 7768}, {"chapterNumber": 32, "chapterTitle": "第三十二章 婚书", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 7550, "processingTime": 7355}, {"chapterNumber": 33, "chapterTitle": "第三十三章 妖种", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 7311, "processingTime": 8927}, {"chapterNumber": 34, "chapterTitle": "第三十四章 小院之战", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 6280, "processingTime": 3931}, {"chapterNumber": 35, "chapterTitle": "第三十五章 仙剑来时", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 7060, "processingTime": 10982}, {"chapterNumber": 36, "chapterTitle": "第三十六章 云至劫来", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 6978, "processingTime": 9751}, {"chapterNumber": 37, "chapterTitle": "第三十七章 心魔历劫", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 6784, "processingTime": 7529}, {"chapterNumber": 38, "chapterTitle": "第三十八章 落雪之城，春寒料峭", "success": false, "error": "没有收到有效的响应内容", "apiKeyUsed": "chat", "processingTime": 62627}, {"chapterNumber": 39, "chapterTitle": "第三十九章 白雪如梦，华裳如炬", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 8549, "processingTime": 10181}, {"chapterNumber": 40, "chapterTitle": "第四十章 心魔领域里的小女孩", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 8625, "processingTime": 15779}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz19lf8uf6ay0qevk", "createdAt": "2025-09-25T06:29:48.596Z", "updatedAt": "2025-09-25T06:32:01.153Z", "result": "成功改写 39/40 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [38], "ruleId": "custom", "status": "failed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 0, "failedChapters": 1, "totalTokensUsed": 0, "totalProcessingTime": 32933, "averageTimePerChapter": 32933, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 38, "chapterTitle": "第三十八章 落雪之城，春寒料峭", "success": false, "error": "没有收到有效的响应内容", "apiKeyUsed": "In The Novel", "processingTime": 32928}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz1f7ykhz9cnptj9ir", "createdAt": "2025-09-25T06:34:11.084Z", "updatedAt": "2025-09-25T06:34:44.019Z", "result": "成功改写 0/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [38], "ruleId": "custom", "status": "failed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 0, "failedChapters": 1, "totalTokensUsed": 0, "totalProcessingTime": 32414, "averageTimePerChapter": 32414, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 38, "chapterTitle": "第三十八章 落雪之城，春寒料峭", "success": false, "error": "没有收到有效的响应内容", "apiKeyUsed": "Generative Language Client", "processingTime": 32406}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz1jnahl163g70ge7", "createdAt": "2025-09-25T06:37:37.577Z", "updatedAt": "2025-09-25T06:38:09.993Z", "result": "成功改写 0/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [1], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 1, "failedChapters": 0, "totalTokensUsed": 5845, "totalProcessingTime": 12261, "averageTimePerChapter": 12261, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 1, "chapterTitle": "第一章 皇城的鬼", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 5845, "processingTime": 12252}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz1s3204fz7var53wa", "createdAt": "2025-09-25T06:44:11.256Z", "updatedAt": "2025-09-25T06:44:23.518Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [38], "ruleId": "custom", "status": "failed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 0, "failedChapters": 1, "totalTokensUsed": 0, "totalProcessingTime": 32382, "averageTimePerChapter": 32382, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 38, "chapterTitle": "第三十八章 落雪之城，春寒料峭", "success": false, "error": "没有收到有效的响应内容", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "processingTime": 32377}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz1t73kaxxj8xdexnu", "createdAt": "2025-09-25T06:45:03.152Z", "updatedAt": "2025-09-25T06:45:35.535Z", "result": "成功改写 0/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [38], "ruleId": "custom", "status": "failed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 0, "failedChapters": 1, "totalTokensUsed": 0, "totalProcessingTime": 33322, "averageTimePerChapter": 33322, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 38, "chapterTitle": "第三十八章 落雪之城，春寒料峭", "success": false, "error": "没有收到有效的响应内容", "apiKeyUsed": "chat", "processingTime": 33315}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz1wv0s8brqpafvfdb", "createdAt": "2025-09-25T06:47:54.124Z", "updatedAt": "2025-09-25T06:48:27.448Z", "result": "成功改写 0/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [1], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 1, "failedChapters": 0, "totalTokensUsed": 5731, "totalProcessingTime": 8127, "averageTimePerChapter": 8127, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 1, "chapterTitle": "第一章 皇城的鬼", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 5731, "processingTime": 8121}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz1ypw5jitbj3344op", "createdAt": "2025-09-25T06:49:20.789Z", "updatedAt": "2025-09-25T06:49:28.923Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [38], "ruleId": "custom", "status": "failed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 0, "failedChapters": 1, "totalTokensUsed": 0, "totalProcessingTime": 32974, "averageTimePerChapter": 32974, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 38, "chapterTitle": "第三十八章 落雪之城，春寒料峭", "success": false, "error": "没有收到有效的响应内容", "apiKeyUsed": "In The Novel", "processingTime": 32968}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz205wfeqy3lx933x", "createdAt": "2025-09-25T06:50:28.191Z", "updatedAt": "2025-09-25T06:51:01.168Z", "result": "成功改写 0/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [38], "ruleId": "custom", "status": "failed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 0, "failedChapters": 1, "totalTokensUsed": 0, "totalProcessingTime": 32840, "averageTimePerChapter": 32840, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 38, "chapterTitle": "第三十八章 落雪之城，春寒料峭", "success": false, "error": "没有收到有效的响应内容", "apiKeyUsed": "My First Project", "processingTime": 32831}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz2o8719glc2bwlcpb", "createdAt": "2025-09-25T07:09:10.909Z", "updatedAt": "2025-09-25T07:09:43.751Z", "result": "成功改写 0/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [38], "ruleId": "custom", "status": "failed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 0, "failedChapters": 1, "totalTokensUsed": 0, "totalProcessingTime": 32985, "averageTimePerChapter": 32985, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 38, "chapterTitle": "第三十八章 落雪之城，春寒料峭", "success": false, "error": "没有收到有效的响应内容", "apiKeyUsed": "chat", "processingTime": 32977}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz2svbw4vgvlaso0u8", "createdAt": "2025-09-25T07:12:47.516Z", "updatedAt": "2025-09-25T07:13:20.503Z", "result": "成功改写 0/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [38], "ruleId": "custom", "status": "failed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 0, "failedChapters": 1, "totalTokensUsed": 0, "totalProcessingTime": 32439, "averageTimePerChapter": 32439, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 38, "chapterTitle": "第三十八章 落雪之城，春寒料峭", "success": false, "error": "没有收到有效的响应内容", "apiKeyUsed": "In The Novel", "processingTime": 32430}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz34fkfvu9qhkdkco", "createdAt": "2025-09-25T07:21:46.959Z", "updatedAt": "2025-09-25T07:22:19.401Z", "result": "成功改写 0/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [38], "ruleId": "custom", "status": "failed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 0, "failedChapters": 1, "totalTokensUsed": 0, "totalProcessingTime": 32940, "averageTimePerChapter": 32940, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 38, "chapterTitle": "第三十八章 落雪之城，春寒料峭", "success": false, "error": "没有收到有效的响应内容", "apiKeyUsed": "chat", "processingTime": 32930}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz36kqjorunpvyx3sa", "createdAt": "2025-09-25T07:23:26.971Z", "updatedAt": "2025-09-25T07:23:59.914Z", "result": "成功改写 0/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [3], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 1, "failedChapters": 0, "totalTokensUsed": 6221, "totalProcessingTime": 9998, "averageTimePerChapter": 9998, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 3, "chapterTitle": "第三章 遇见一个自己", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 6221, "processingTime": 9990}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz39l7q7llzqqk1sxc", "createdAt": "2025-09-25T07:25:47.558Z", "updatedAt": "2025-09-25T07:25:57.559Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [38], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 1, "failedChapters": 0, "totalTokensUsed": 7594, "totalProcessingTime": 15470, "averageTimePerChapter": 15470, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 38, "chapterTitle": "第三十八章 落雪之城，春寒料峭", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 7594, "processingTime": 15461}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz52sk5z3mpbof6f1p", "createdAt": "2025-09-25T08:16:29.717Z", "updatedAt": "2025-09-25T08:16:45.189Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [38], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 1, "failedChapters": 0, "totalTokensUsed": 6343, "totalProcessingTime": 16786, "averageTimePerChapter": 16786, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 38, "chapterTitle": "第三十八章 落雪之城，春寒料峭", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 6343, "processingTime": 16779}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz53rga860r8ktx82f", "createdAt": "2025-09-25T08:17:14.938Z", "updatedAt": "2025-09-25T08:17:31.726Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "2108612704c4f3eb95", "chapters": [1], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 1, "failedChapters": 0, "totalTokensUsed": 6160, "totalProcessingTime": 8447, "averageTimePerChapter": 8447, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 1, "chapterTitle": "第一章 皇城的鬼", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 6160, "processingTime": 8436}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mfz5b8bgrntgckayxvk", "createdAt": "2025-09-25T08:23:03.388Z", "updatedAt": "2025-09-25T08:23:11.838Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "0620431a73f462e4f3", "chapters": [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 301, "completedChapters": 300, "failedChapters": 1, "totalTokensUsed": 1148296, "totalProcessingTime": 740300, "averageTimePerChapter": 2459.468438538206, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 100, "chapterTitle": "第99章 关系就是生产力", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3758, "processingTime": 6914}, {"chapterNumber": 101, "chapterTitle": "第100章 “水箭龟”王梓博", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3566, "processingTime": 5964}, {"chapterNumber": 102, "chapterTitle": "第101章 不想当渣男", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3367, "processingTime": 6689}, {"chapterNumber": 103, "chapterTitle": "第102章 你挑着担，我牵着马", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3548, "processingTime": 5939}, {"chapterNumber": 104, "chapterTitle": "第103章 心尖尖", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3900, "processingTime": 7440}, {"chapterNumber": 105, "chapterTitle": "第104章 还真有个表妹", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3274, "processingTime": 3521}, {"chapterNumber": 106, "chapterTitle": "第105章 四川的一晚（上）", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3742, "processingTime": 6807}, {"chapterNumber": 107, "chapterTitle": "第106章 四川的一晚（下）", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3421, "processingTime": 7411}, {"chapterNumber": 108, "chapterTitle": "第107章 12分的高数", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3146, "processingTime": 5611}, {"chapterNumber": 109, "chapterTitle": "第108章 生活系无赖", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3976, "processingTime": 6998}, {"chapterNumber": 110, "chapterTitle": "第109章 聚会时的小意外", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3257, "processingTime": 6457}, {"chapterNumber": 111, "chapterTitle": "第110章 2002版以身相许", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3082, "processingTime": 4686}, {"chapterNumber": 112, "chapterTitle": "第111章 瑞雪兆丰年", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 4071, "processingTime": 9126}, {"chapterNumber": 113, "chapterTitle": "第112章 一个被放弃的大号", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3358, "processingTime": 7458}, {"chapterNumber": 114, "chapterTitle": "第113章 修罗场（上）", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 4202, "processingTime": 12010}, {"chapterNumber": 115, "chapterTitle": "第114章 修罗场（下）", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 4121, "processingTime": 7207}, {"chapterNumber": 116, "chapterTitle": "第115章 新部长", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 3524, "processingTime": 4537}, {"chapterNumber": 117, "chapterTitle": "第116章 讹车", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 3517, "processingTime": 6292}, {"chapterNumber": 118, "chapterTitle": "第117章 我想给你买辆车", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3482, "processingTime": 7356}, {"chapterNumber": 119, "chapterTitle": "第118章 珍惜简单", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4191, "processingTime": 14539}, {"chapterNumber": 120, "chapterTitle": "第119章 干就完事了", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3514, "processingTime": 6555}, {"chapterNumber": 121, "chapterTitle": "第120章 当我是提款机？", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 2713, "processingTime": 2815}, {"chapterNumber": 122, "chapterTitle": "第121章 醋味的春天", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3614, "processingTime": 8516}, {"chapterNumber": 123, "chapterTitle": "第122章 800块和80块", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3735, "processingTime": 10933}, {"chapterNumber": 124, "chapterTitle": "第123章 萝卜青菜，各有所爱", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3144, "processingTime": 4962}, {"chapterNumber": 125, "chapterTitle": "第124章 设套", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3037, "processingTime": 5731}, {"chapterNumber": 126, "chapterTitle": "第125章 我是大恶人", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3498, "processingTime": 6729}, {"chapterNumber": 127, "chapterTitle": "第126章 陈汉升配不上女菩萨", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3199, "processingTime": 3978}, {"chapterNumber": 128, "chapterTitle": "第127章 全院的新生妹妹们", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3526, "processingTime": 9487}, {"chapterNumber": 129, "chapterTitle": "第128章 一定要抱最粗的大腿", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3190, "processingTime": 2410}, {"chapterNumber": 130, "chapterTitle": "第129章 我是法人郑观媞", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3617, "processingTime": 6252}, {"chapterNumber": 131, "chapterTitle": "第130章 隔离乱象", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 3789, "processingTime": 5799}, {"chapterNumber": 132, "chapterTitle": "第131章 神仙和劫数", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 3876, "processingTime": 5391}, {"chapterNumber": 133, "chapterTitle": "第132章 门里门外", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 5365, "processingTime": 11733}, {"chapterNumber": 134, "chapterTitle": "第133章 我也能叫你小陈吗？", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 4802, "processingTime": 7066}, {"chapterNumber": 135, "chapterTitle": "第134章 我与春风皆过客", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 5020, "processingTime": 10722}, {"chapterNumber": 136, "chapterTitle": "第135章 踢馆", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3330, "processingTime": 4140}, {"chapterNumber": 137, "chapterTitle": "第136章 偏执的小师妹", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3530, "processingTime": 6936}, {"chapterNumber": 138, "chapterTitle": "第137章 叫你爸爸不吃亏", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3086, "processingTime": 4462}, {"chapterNumber": 139, "chapterTitle": "第138章 QQ签名真是好东西", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3455, "processingTime": 7388}, {"chapterNumber": 140, "chapterTitle": "第139章 东风这张好牌", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3305, "processingTime": 5485}, {"chapterNumber": 141, "chapterTitle": "第140章 看那天边的云彩哟", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3416, "processingTime": 7607}, {"chapterNumber": 142, "chapterTitle": "第141章 无渣不浪", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3451, "processingTime": 8776}, {"chapterNumber": 143, "chapterTitle": "第142章 谁在孤立谁？", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3528, "processingTime": 6553}, {"chapterNumber": 144, "chapterTitle": "第143章 哥也不容易", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 2893, "processingTime": 2732}, {"chapterNumber": 145, "chapterTitle": "第144章 研究沈幼楚", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4524, "processingTime": 14947}, {"chapterNumber": 146, "chapterTitle": "第145章 一句“好久不见”", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3605, "processingTime": 6262}, {"chapterNumber": 147, "chapterTitle": "第146章 默契的“前任”", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3175, "processingTime": 6959}, {"chapterNumber": 148, "chapterTitle": "第147章 醉美人", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3460, "processingTime": 6698}, {"chapterNumber": 149, "chapterTitle": "第148章 女人都是感性的", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 3239, "processingTime": 5520}, {"chapterNumber": 150, "chapterTitle": "第149章 只存一个号码的小灵通", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3494, "processingTime": 8687}, {"chapterNumber": 151, "chapterTitle": "第150章 谁是懂法律的人", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3730, "processingTime": 7826}, {"chapterNumber": 152, "chapterTitle": "第151章 帮你做英雄", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3108, "processingTime": 4318}, {"chapterNumber": 153, "chapterTitle": "第152章 别叫姐姐，要叫妈", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3989, "processingTime": 7666}, {"chapterNumber": 154, "chapterTitle": "第153章 演砸了", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4152, "processingTime": 14538}, {"chapterNumber": 155, "chapterTitle": "第154章 他不要我了", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3772, "processingTime": 10082}, {"chapterNumber": 156, "chapterTitle": "第155章 迷离的夜（上）", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3926, "processingTime": 9787}, {"chapterNumber": 157, "chapterTitle": "第156章 迷离的夜（中）", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4125, "processingTime": 8639}, {"chapterNumber": 158, "chapterTitle": "第157章 迷离的夜（下）", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3241, "processingTime": 4593}, {"chapterNumber": 159, "chapterTitle": "第158章 校园爱情故事", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3022, "processingTime": 4035}, {"chapterNumber": 160, "chapterTitle": "第159章 胸口勒的紧", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3704, "processingTime": 5895}, {"chapterNumber": 161, "chapterTitle": "第160章 金老师和金班长", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3689, "processingTime": 7341}, {"chapterNumber": 162, "chapterTitle": "第161章 朝霞下的自我介绍", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4077, "processingTime": 7395}, {"chapterNumber": 163, "chapterTitle": "第162章 说话的艺术", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4072, "processingTime": 11557}, {"chapterNumber": 164, "chapterTitle": "第163章 打工是不可能打工的", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3177, "processingTime": 5671}, {"chapterNumber": 165, "chapterTitle": "第164章 男闺蜜", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3328, "processingTime": 4356}, {"chapterNumber": 166, "chapterTitle": "第165章 他那么闷骚", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 2932, "processingTime": 2434}, {"chapterNumber": 167, "chapterTitle": "第166章 没资格吃的醋最酸", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 3438, "processingTime": 4302}, {"chapterNumber": 168, "chapterTitle": "第167章 如果你没遇到陈汉升", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3821, "processingTime": 9783}, {"chapterNumber": 169, "chapterTitle": "第168章 潇洒的渣男", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 8933, "processingTime": 21708}, {"chapterNumber": 170, "chapterTitle": "第169章 宝藏终将璀璨", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 4620, "processingTime": 9471}, {"chapterNumber": 171, "chapterTitle": "第170章 咱也混混文化圈", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3697, "processingTime": 11420}, {"chapterNumber": 172, "chapterTitle": "第171章 砸店", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3209, "processingTime": 8164}, {"chapterNumber": 173, "chapterTitle": "第172章 看我牛逼不？", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 4501, "processingTime": 11641}, {"chapterNumber": 174, "chapterTitle": "第173章 可能，我漂亮吧", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3264, "processingTime": 5853}, {"chapterNumber": 175, "chapterTitle": "第174章 拯救年轻的灵魂", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3360, "processingTime": 5072}, {"chapterNumber": 176, "chapterTitle": "第175章 我也不要！", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 3664, "processingTime": 5096}, {"chapterNumber": 177, "chapterTitle": "第176章 比舔狗还惨的陈汉升", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 3823, "processingTime": 7518}, {"chapterNumber": 178, "chapterTitle": "第177章 笑脸讲道理，翻脸讲规矩", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 3388, "processingTime": 4290}, {"chapterNumber": 179, "chapterTitle": "第178章 存折", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 4173, "processingTime": 8235}, {"chapterNumber": 180, "chapterTitle": "第179章 体育场的奶茶店", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3192, "processingTime": 7319}, {"chapterNumber": 181, "chapterTitle": "第180章 加钱居士", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3672, "processingTime": 9294}, {"chapterNumber": 182, "chapterTitle": "第181章 渣男何苦为难渣男", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3493, "processingTime": 6506}, {"chapterNumber": 183, "chapterTitle": "第182章 意外不断的考察", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3434, "processingTime": 10082}, {"chapterNumber": 184, "chapterTitle": "第183章 他怎么会在这里？", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3072, "processingTime": 4340}, {"chapterNumber": 185, "chapterTitle": "第184章 不是风流债", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3515, "processingTime": 9142}, {"chapterNumber": 186, "chapterTitle": "第185章 爸爸的特长", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 4452, "processingTime": 10936}, {"chapterNumber": 187, "chapterTitle": "第186章 真实酒吧装逼指南", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3250, "processingTime": 7526}, {"chapterNumber": 188, "chapterTitle": "第187章 对舔狗的致命一击", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3164, "processingTime": 4779}, {"chapterNumber": 189, "chapterTitle": "第188章 当面教学的陈大师", "success": false, "error": "内容被检测为违规内容", "apiKeyUsed": "In The Novel", "processingTime": 73342}, {"chapterNumber": 190, "chapterTitle": "第189章 帮你报仇怎么样？", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3718, "processingTime": 8882}, {"chapterNumber": 191, "chapterTitle": "第190章 人间祸害", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 3022, "processingTime": 3596}, {"chapterNumber": 192, "chapterTitle": "第191章 报应来了", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3099, "processingTime": 6217}, {"chapterNumber": 193, "chapterTitle": "第192章 甜甜的秋运会啊", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3427, "processingTime": 6698}, {"chapterNumber": 194, "chapterTitle": "第193章 狗粮凶猛", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3452, "processingTime": 8084}, {"chapterNumber": 195, "chapterTitle": "第194章 青春就要搞事", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 2975, "processingTime": 3679}, {"chapterNumber": 196, "chapterTitle": "第195章 火箭101的扩张进度表", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 2675, "processingTime": 3432}, {"chapterNumber": 197, "chapterTitle": "第196章 操场的杂物房", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3193, "processingTime": 8519}, {"chapterNumber": 198, "chapterTitle": "第197章 憨儿", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3508, "processingTime": 8253}, {"chapterNumber": 199, "chapterTitle": "第198章 有灵魂的奶茶店", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3451, "processingTime": 20813}, {"chapterNumber": 200, "chapterTitle": "第199章 渣男是什么？", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3920, "processingTime": 22715}, {"chapterNumber": 201, "chapterTitle": "第200章 1000万的爱情门槛", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3182, "processingTime": 5742}, {"chapterNumber": 202, "chapterTitle": "第201章 小城小事", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3341, "processingTime": 5975}, {"chapterNumber": 203, "chapterTitle": "第202章 1000万就是个弟中弟", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 4890, "processingTime": 17719}, {"chapterNumber": 204, "chapterTitle": "第203章 小夏利也能装逼了", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3117, "processingTime": 4714}, {"chapterNumber": 205, "chapterTitle": "第204章 我应该在车底，不应该在车里", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3966, "processingTime": 9209}, {"chapterNumber": 206, "chapterTitle": "第205章 听大佬讲故事", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3243, "processingTime": 5761}, {"chapterNumber": 207, "chapterTitle": "第206章 多喝热水", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3825, "processingTime": 9003}, {"chapterNumber": 208, "chapterTitle": "第207章 晚安，坏蛋", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3394, "processingTime": 7357}, {"chapterNumber": 209, "chapterTitle": "第208章 未来的定位", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3318, "processingTime": 4870}, {"chapterNumber": 210, "chapterTitle": "第209章 一定要做点什么", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3205, "processingTime": 4609}, {"chapterNumber": 211, "chapterTitle": "第210章 醉了才有机会", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 2970, "processingTime": 5089}, {"chapterNumber": 212, "chapterTitle": "第211章 我全都要.GIF", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4062, "processingTime": 13549}, {"chapterNumber": 213, "chapterTitle": "第212章 撩啊撩的", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3096, "processingTime": 6602}, {"chapterNumber": 214, "chapterTitle": "第213章 憨包老板娘", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3008, "processingTime": 4596}, {"chapterNumber": 215, "chapterTitle": "第214章 拉高整个财院的欣赏水平", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3217, "processingTime": 6082}, {"chapterNumber": 216, "chapterTitle": "第215章 无间道之渣男无间", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4669, "processingTime": 13400}, {"chapterNumber": 217, "chapterTitle": "第216章 周六电影不打烊", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4369, "processingTime": 11961}, {"chapterNumber": 218, "chapterTitle": "第217章 百因必有果，你的报应就是我", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3647, "processingTime": 6576}, {"chapterNumber": 219, "chapterTitle": "第218章 今日降温，注意保暖", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3310, "processingTime": 5239}, {"chapterNumber": 220, "chapterTitle": "第219章 层出不穷的套路", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4130, "processingTime": 9697}, {"chapterNumber": 221, "chapterTitle": "第220章 防不胜防啊", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4211, "processingTime": 11473}, {"chapterNumber": 222, "chapterTitle": "第221章 我只是搂着你说说话", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4304, "processingTime": 10989}, {"chapterNumber": 223, "chapterTitle": "第222章 没想到吧，又翻车了！", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3465, "processingTime": 8068}, {"chapterNumber": 224, "chapterTitle": "第223章 你想多个儿媳妇，还是想少个儿子？", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 7373, "processingTime": 16799}, {"chapterNumber": 225, "chapterTitle": "第224章 我在他心里排第二", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 7177, "processingTime": 16161}, {"chapterNumber": 226, "chapterTitle": "第225章 傻子室友欢乐多", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 3334, "processingTime": 5264}, {"chapterNumber": 227, "chapterTitle": "第226章 想当宝藏女孩的胡林语", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 3887, "processingTime": 6170}, {"chapterNumber": 228, "chapterTitle": "第227章 我是你媞哥", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 2779, "processingTime": 2553}, {"chapterNumber": 229, "chapterTitle": "第228章 郑观媞的家", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4250, "processingTime": 12321}, {"chapterNumber": 230, "chapterTitle": "第229章 舔狗的“在吗”", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3360, "processingTime": 4546}, {"chapterNumber": 231, "chapterTitle": "第230章 潘多拉的魔盒", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3523, "processingTime": 7665}, {"chapterNumber": 232, "chapterTitle": "第231章 奶茶店开业啦", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3615, "processingTime": 6073}, {"chapterNumber": 233, "chapterTitle": "第232章 商妍妍是个好女孩", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3243, "processingTime": 4023}, {"chapterNumber": 234, "chapterTitle": "第233章 忍让解决不了问题", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 2838, "processingTime": 2115}, {"chapterNumber": 235, "chapterTitle": "第234章 新仇和旧恨", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 2895, "processingTime": 3946}, {"chapterNumber": 236, "chapterTitle": "第235章 谈不妥怎么办？", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3122, "processingTime": 4117}, {"chapterNumber": 237, "chapterTitle": "第236章 吃串串磕掉一颗牙", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 3082, "processingTime": 3352}, {"chapterNumber": 238, "chapterTitle": "第237章 假扮你的男朋友？", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 3823, "processingTime": 5137}, {"chapterNumber": 239, "chapterTitle": "第238章 射手座又浪又有趣", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 2932, "processingTime": 2346}, {"chapterNumber": 240, "chapterTitle": "第239章 萧容鱼VS罗璇", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3459, "processingTime": 4768}, {"chapterNumber": 241, "chapterTitle": "第240章 “小陈”不是谁都能叫的", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 3941, "processingTime": 6736}, {"chapterNumber": 242, "chapterTitle": "第241章 渣男的感情就是雾里看花", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4004, "processingTime": 9780}, {"chapterNumber": 243, "chapterTitle": "第242章 星巴克和烤红薯", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3345, "processingTime": 5944}, {"chapterNumber": 244, "chapterTitle": "第243章 你是个好人", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 2951, "processingTime": 3449}, {"chapterNumber": 245, "chapterTitle": "第244章 宝宝是宝宝，宝贝是宝贝", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3504, "processingTime": 3290}, {"chapterNumber": 246, "chapterTitle": "第245章 再见，郑公主", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3953, "processingTime": 6854}, {"chapterNumber": 247, "chapterTitle": "第246章 你在别人眼里的样子", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3962, "processingTime": 7048}, {"chapterNumber": 248, "chapterTitle": "第247章 秀恩爱，死得快", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3307, "processingTime": 2218}, {"chapterNumber": 249, "chapterTitle": "第248章 我的心愿是世界和平", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3624, "processingTime": 4379}, {"chapterNumber": 250, "chapterTitle": "第249章 我不想看到你被骂", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 3467, "processingTime": 5513}, {"chapterNumber": 251, "chapterTitle": "第250章 王梓博的机遇", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 3957, "processingTime": 7830}, {"chapterNumber": 252, "chapterTitle": "第251章 提前两年半出世的宝藏", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 3477, "processingTime": 5403}, {"chapterNumber": 253, "chapterTitle": "第252章 真正的渣男", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4496, "processingTime": 20387}, {"chapterNumber": 254, "chapterTitle": "第253章 你好，我叫陈汉升", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3590, "processingTime": 10773}, {"chapterNumber": 255, "chapterTitle": "第254章 报复进行时（上）", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3085, "processingTime": 1945}, {"chapterNumber": 256, "chapterTitle": "第255章 报复进行时（中）", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4017, "processingTime": 7460}, {"chapterNumber": 257, "chapterTitle": "第256章 报复进行时（下）", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3106, "processingTime": 2498}, {"chapterNumber": 258, "chapterTitle": "第257章 恶人自有恶人磨", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3063, "processingTime": 2887}, {"chapterNumber": 259, "chapterTitle": "第258章 大家都在演戏", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3629, "processingTime": 5149}, {"chapterNumber": 260, "chapterTitle": "第259章 三方会谈正式开启", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 2995, "processingTime": 2992}, {"chapterNumber": 261, "chapterTitle": "第260章 我能把他整自闭了", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3074, "processingTime": 3588}, {"chapterNumber": 262, "chapterTitle": "第261章 财院女生守护神", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3523, "processingTime": 4391}, {"chapterNumber": 263, "chapterTitle": "第262章 沈幼楚抬头的这一天", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3795, "processingTime": 7863}, {"chapterNumber": 264, "chapterTitle": "第263章 安排罗璇", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4119, "processingTime": 8337}, {"chapterNumber": 265, "chapterTitle": "第264章 罐子里的小星星", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4222, "processingTime": 6884}, {"chapterNumber": 266, "chapterTitle": "第265章 相煎何太急", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3755, "processingTime": 5718}, {"chapterNumber": 267, "chapterTitle": "第266章 我赌你的枪里没有子弹", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4733, "processingTime": 8695}, {"chapterNumber": 268, "chapterTitle": "第267章 讨好型人格的舔狗", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 2847, "processingTime": 1634}, {"chapterNumber": 269, "chapterTitle": "第268章 暖暖的幸福", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3657, "processingTime": 5582}, {"chapterNumber": 270, "chapterTitle": "第269章 喝完酒脸疼", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3523, "processingTime": 8551}, {"chapterNumber": 271, "chapterTitle": "第270章 再当舔狗，我就把姓倒过来", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4111, "processingTime": 8713}, {"chapterNumber": 272, "chapterTitle": "第271章 冲锋你来，翻盘靠我", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4023, "processingTime": 4987}, {"chapterNumber": 273, "chapterTitle": "第272章 校园爱情故事", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3778, "processingTime": 6479}, {"chapterNumber": 274, "chapterTitle": "第273章 人生总是在不断的抉择", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4212, "processingTime": 8888}, {"chapterNumber": 275, "chapterTitle": "第274章 多半个小时的意义", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3911, "processingTime": 7673}, {"chapterNumber": 276, "chapterTitle": "第275章 渣男的火树银花", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 4488, "processingTime": 10287}, {"chapterNumber": 277, "chapterTitle": "第276章 我为你走了99步，你能走一步吗？", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3751, "processingTime": 4621}, {"chapterNumber": 278, "chapterTitle": "第277章 真实的坏人", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3929, "processingTime": 6669}, {"chapterNumber": 279, "chapterTitle": "第278章 打架那件事", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3706, "processingTime": 5987}, {"chapterNumber": 280, "chapterTitle": "第279章 喜怒无常的小陈", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3795, "processingTime": 5599}, {"chapterNumber": 281, "chapterTitle": "第280章 既忽悠大学生，又欺骗小女孩", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3738, "processingTime": 5498}, {"chapterNumber": 282, "chapterTitle": "第281章 偷奸又耍滑", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3546, "processingTime": 4912}, {"chapterNumber": 283, "chapterTitle": "第282章 人是我的，厂也是我的", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3372, "processingTime": 7619}, {"chapterNumber": 284, "chapterTitle": "第283章 一个标点都不能信", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3192, "processingTime": 7543}, {"chapterNumber": 285, "chapterTitle": "第284章 职场就是一场暗战", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3410, "processingTime": 9043}, {"chapterNumber": 286, "chapterTitle": "第285章 中国的郑布斯", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 3631, "processingTime": 5810}, {"chapterNumber": 287, "chapterTitle": "第286章 火箭101终于败露了", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 2659, "processingTime": 2553}, {"chapterNumber": 288, "chapterTitle": "第287章 女人30岁", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3107, "processingTime": 3367}, {"chapterNumber": 289, "chapterTitle": "第288章 十年", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 4218, "processingTime": 8830}, {"chapterNumber": 290, "chapterTitle": "第289章 打赌让你渣一回", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 4437, "processingTime": 9593}, {"chapterNumber": 291, "chapterTitle": "第290章 大学生创业合伙人", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3063, "processingTime": 4087}, {"chapterNumber": 292, "chapterTitle": "第291章 小门小户的婚姻观", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3707, "processingTime": 4993}, {"chapterNumber": 293, "chapterTitle": "第292章 第一次捏脚的小事故", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3490, "processingTime": 5726}, {"chapterNumber": 294, "chapterTitle": "第293章 大龄女生相亲记", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3592, "processingTime": 4705}, {"chapterNumber": 295, "chapterTitle": "第294章 你可以赚，但我绝对不亏", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 2828, "processingTime": 2186}, {"chapterNumber": 296, "chapterTitle": "第295章 世间美好，和你环环相扣", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 5227, "processingTime": 18851}, {"chapterNumber": 297, "chapterTitle": "第296章 渣女的套路", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3319, "processingTime": 9042}, {"chapterNumber": 298, "chapterTitle": "第297章 我是来相亲的", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4125, "processingTime": 12920}, {"chapterNumber": 299, "chapterTitle": "第298章 不请我上楼坐一坐？", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 4411, "processingTime": 8866}, {"chapterNumber": 300, "chapterTitle": "第299章 陈汉升的人格魅力", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 4523, "processingTime": 6188}, {"chapterNumber": 301, "chapterTitle": "第300章 刻意的浓妆", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4234, "processingTime": 7756}, {"chapterNumber": 302, "chapterTitle": "第301章 老教授、校花和流氓", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4688, "processingTime": 5170}, {"chapterNumber": 303, "chapterTitle": "第302章 傲娇女生的爱情观", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3493, "processingTime": 5731}, {"chapterNumber": 304, "chapterTitle": "第303章 成年人的关系", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4750, "processingTime": 8810}, {"chapterNumber": 305, "chapterTitle": "第304章 震惊，申通公司居然要倒闭了！", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3119, "processingTime": 3666}, {"chapterNumber": 306, "chapterTitle": "第305章 生活里的棒子和糖果", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 5324, "processingTime": 7274}, {"chapterNumber": 307, "chapterTitle": "第306章 当你老了，头发白了", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 6008, "processingTime": 12229}, {"chapterNumber": 308, "chapterTitle": "第307章 萧容鱼和沈幼楚的未来身份雏形", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3992, "processingTime": 4603}, {"chapterNumber": 309, "chapterTitle": "第308章 浪漫的情怀", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4141, "processingTime": 4856}, {"chapterNumber": 310, "chapterTitle": "第309章 中国式内向家庭关系", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 5381, "processingTime": 9113}, {"chapterNumber": 311, "chapterTitle": "第310章 圈子里多了个绿茶", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4427, "processingTime": 5523}, {"chapterNumber": 312, "chapterTitle": "第311章 《亮剑》的启示", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 4414, "processingTime": 8496}, {"chapterNumber": 313, "chapterTitle": "第312章 年底事情特别多", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3962, "processingTime": 5343}, {"chapterNumber": 314, "chapterTitle": "第313章 Double kill", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3266, "processingTime": 4250}, {"chapterNumber": 315, "chapterTitle": "第314章 东大研一陈汉升，请指教", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3327, "processingTime": 2942}, {"chapterNumber": 316, "chapterTitle": "第315章 我怎么就是亲戚了？", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 4296, "processingTime": 6644}, {"chapterNumber": 317, "chapterTitle": "第316章 天啦，小鱼儿忘记陈汉升了！", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 4033, "processingTime": 3246}, {"chapterNumber": 318, "chapterTitle": "第317章 有话躺下说", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 4729, "processingTime": 6269}, {"chapterNumber": 319, "chapterTitle": "第318章 如何每门考到“80分”", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 4409, "processingTime": 8054}, {"chapterNumber": 320, "chapterTitle": "第319章 高端渣男的手段", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 3873, "processingTime": 5965}, {"chapterNumber": 321, "chapterTitle": "第320章 所谓的“高端”，就是不接地气？", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3425, "processingTime": 4679}, {"chapterNumber": 322, "chapterTitle": "第321章 从今天开始羡慕你", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4690, "processingTime": 12314}, {"chapterNumber": 323, "chapterTitle": "第322章 咱们当兄妹吧", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4569, "processingTime": 15612}, {"chapterNumber": 324, "chapterTitle": "第323章 查岗时，应该怎么做？", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3671, "processingTime": 8214}, {"chapterNumber": 325, "chapterTitle": "第324章 嗯？", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 5010, "processingTime": 17226}, {"chapterNumber": 326, "chapterTitle": "第325章 女人就是爱翻旧账", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 4934, "processingTime": 7634}, {"chapterNumber": 327, "chapterTitle": "第326章 横冲直撞2004年", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3569, "processingTime": 3895}, {"chapterNumber": 328, "chapterTitle": "第327章 第一次坐飞机，如何装作经常坐的样子？", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3895, "processingTime": 7146}, {"chapterNumber": 329, "chapterTitle": "第328章 恋爱不该隐瞒", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3452, "processingTime": 4509}, {"chapterNumber": 330, "chapterTitle": "第329章 陈氏猜硬币法", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 4590, "processingTime": 9482}, {"chapterNumber": 331, "chapterTitle": "第330章 山里的一朵花", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 3553, "processingTime": 5608}, {"chapterNumber": 332, "chapterTitle": "第331章 柴米油盐酱醋茶的爱情", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 4130, "processingTime": 8114}, {"chapterNumber": 333, "chapterTitle": "第332章 你哭你们的，我睡我的", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 4990, "processingTime": 10088}, {"chapterNumber": 334, "chapterTitle": "第333章 呔，你这可恶又可爱的妖怪！", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 4783, "processingTime": 9573}, {"chapterNumber": 335, "chapterTitle": "第334章 你那是眼馋人家！", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 4899, "processingTime": 11945}, {"chapterNumber": 336, "chapterTitle": "第335章 到底是哪个姓", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4136, "processingTime": 6984}, {"chapterNumber": 337, "chapterTitle": "第336章 一家人的小欢喜", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3538, "processingTime": 4955}, {"chapterNumber": 338, "chapterTitle": "第337章 来自路虎的俯视", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3436, "processingTime": 3465}, {"chapterNumber": 339, "chapterTitle": "第338章 萧奶奶的灵魂两连击", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4139, "processingTime": 6969}, {"chapterNumber": 340, "chapterTitle": "第339章 陈渣男VS钢铁侠", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4852, "processingTime": 7545}, {"chapterNumber": 341, "chapterTitle": "第340章 买个车再吵个架，完美！", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3705, "processingTime": 5629}, {"chapterNumber": 342, "chapterTitle": "第341章 陈哥的套路有十八个弯", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4485, "processingTime": 5308}, {"chapterNumber": 343, "chapterTitle": "第342章 有趣有爱的老太太", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4047, "processingTime": 5919}, {"chapterNumber": 344, "chapterTitle": "第343章 借钱", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4266, "processingTime": 6828}, {"chapterNumber": 345, "chapterTitle": "第344章 摸一下要8000", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3871, "processingTime": 5315}, {"chapterNumber": 346, "chapterTitle": "第345章 女人背后的男人", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3625, "processingTime": 4884}, {"chapterNumber": 347, "chapterTitle": "第346章 相爱又相杀", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4683, "processingTime": 9841}, {"chapterNumber": 348, "chapterTitle": "第347章 再多真的伤不起了", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4258, "processingTime": 7568}, {"chapterNumber": 349, "chapterTitle": "第348章 人生中总会遇到一些没有感情，但又放不下的异性", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4627, "processingTime": 6737}, {"chapterNumber": 350, "chapterTitle": "第349章 陈汉升的人情世故", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 5013, "processingTime": 7653}, {"chapterNumber": 351, "chapterTitle": "第350章 陈汉升的妹妹", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3606, "processingTime": 4767}, {"chapterNumber": 352, "chapterTitle": "第351章 可爱又美好的生活啊", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 4029, "processingTime": 6710}, {"chapterNumber": 353, "chapterTitle": "第352章 世界很温柔，做个贪财好色的俗人吧（上）", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 4342, "processingTime": 9126}, {"chapterNumber": 354, "chapterTitle": "第353章 世界很温柔，做个贪财好色的俗人吧（下）", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 5792, "processingTime": 13157}, {"chapterNumber": 355, "chapterTitle": "第354章 财院602的鬼故事", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4583, "processingTime": 10042}, {"chapterNumber": 356, "chapterTitle": "第355章 渣男的智慧", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3779, "processingTime": 6261}, {"chapterNumber": 357, "chapterTitle": "第356章 寒假开学的第一天", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4747, "processingTime": 13263}, {"chapterNumber": 358, "chapterTitle": "第357章 得偿所愿的陈副主席", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3893, "processingTime": 5854}, {"chapterNumber": 359, "chapterTitle": "第358章 哄好女生的技巧", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3891, "processingTime": 5866}, {"chapterNumber": 360, "chapterTitle": "第359章 男人有钱就变坏", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3884, "processingTime": 6179}, {"chapterNumber": 361, "chapterTitle": "第360章 老陈的往事", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3340, "processingTime": 2386}, {"chapterNumber": 362, "chapterTitle": "第361章 似水流年，抵不过我的发妻", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 3990, "processingTime": 6869}, {"chapterNumber": 363, "chapterTitle": "第362章 我想成为你这样的女人", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 3803, "processingTime": 4039}, {"chapterNumber": 364, "chapterTitle": "第363章 你居然叫我好男人？", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 4758, "processingTime": 7504}, {"chapterNumber": 365, "chapterTitle": "第364章 论套路还是陈哥多一点", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 3696, "processingTime": 3637}, {"chapterNumber": 366, "chapterTitle": "第365章 处处都是陷阱的商业谈判", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3450, "processingTime": 2992}, {"chapterNumber": 367, "chapterTitle": "第366章 广播站播音事件", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3428, "processingTime": 4496}, {"chapterNumber": 368, "chapterTitle": "第367章 皱巴巴的可疑纸团", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3870, "processingTime": 3846}, {"chapterNumber": 369, "chapterTitle": "第368章 温顺的沈幼楚和心酸的萧容鱼", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3697, "processingTime": 5835}, {"chapterNumber": 370, "chapterTitle": "第369章 狮子桥的偶遇", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3925, "processingTime": 9289}, {"chapterNumber": 371, "chapterTitle": "第370章 丁克和素食的有钱家庭", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3746, "processingTime": 9869}, {"chapterNumber": 372, "chapterTitle": "第371章 男闺蜜，我想抱抱你", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3506, "processingTime": 6381}, {"chapterNumber": 373, "chapterTitle": "第372章 一碗鸭血粉丝汤的温度", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 5162, "processingTime": 11604}, {"chapterNumber": 374, "chapterTitle": "第373章 尴尬的“争宠”事件", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3481, "processingTime": 5685}, {"chapterNumber": 375, "chapterTitle": "第374章 电视台的女记者", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3732, "processingTime": 5204}, {"chapterNumber": 376, "chapterTitle": "第375章 当风花雪月遇到现实", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 3576, "processingTime": 4327}, {"chapterNumber": 377, "chapterTitle": "第376章 夭寿，渣男居然被养胎了！", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 4741, "processingTime": 4951}, {"chapterNumber": 378, "chapterTitle": "第377章 哄女孩，我在行！", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3564, "processingTime": 5605}, {"chapterNumber": 379, "chapterTitle": "第378章 双鱼女孩的二十岁生日", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3694, "processingTime": 5079}, {"chapterNumber": 380, "chapterTitle": "第379章 我们要谈男人，你也要听吗？", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4685, "processingTime": 8651}, {"chapterNumber": 381, "chapterTitle": "第380章 病娇的罗璇和狗头军师胡林语", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3739, "processingTime": 6736}, {"chapterNumber": 382, "chapterTitle": "第381章 陈总的便宜不能随便占", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4100, "processingTime": 6939}, {"chapterNumber": 383, "chapterTitle": "第382章 一碗水要端平", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4242, "processingTime": 7029}, {"chapterNumber": 384, "chapterTitle": "第383章 孩子是父母的心头肉", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 2977, "processingTime": 1756}, {"chapterNumber": 385, "chapterTitle": "第384章 价值200块的酒店司仪", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4937, "processingTime": 8559}, {"chapterNumber": 386, "chapterTitle": "第385章 悄悄的进村，打枪的不要", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 3362, "processingTime": 2468}, {"chapterNumber": 387, "chapterTitle": "第386章 我的白月光，生日快乐！", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 7423, "processingTime": 14151}, {"chapterNumber": 388, "chapterTitle": "第387章 养了二十年的姑娘啊，从此长大了！", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4323, "processingTime": 7802}, {"chapterNumber": 389, "chapterTitle": "第388章 有背景就是为所欲为", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3829, "processingTime": 8311}, {"chapterNumber": 390, "chapterTitle": "第389章 老陈家的“大魔王”", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3634, "processingTime": 6317}, {"chapterNumber": 391, "chapterTitle": "第390章 儿子，你配不上萧容鱼", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 5806, "processingTime": 7154}, {"chapterNumber": 392, "chapterTitle": "第391章 准备回财院装X", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 3579, "processingTime": 5665}, {"chapterNumber": 393, "chapterTitle": "第392章 想搞事？我陪你！", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 4031, "processingTime": 6015}, {"chapterNumber": 394, "chapterTitle": "第393章 许久不见的宿舍茶话会", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3500, "processingTime": 3024}, {"chapterNumber": 395, "chapterTitle": "第394章 我真没想当渣女", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 3707, "processingTime": 7777}, {"chapterNumber": 396, "chapterTitle": "第395章 成熟男人戴手牌", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 2853, "processingTime": 2389}, {"chapterNumber": 397, "chapterTitle": "第396章 财大之星陈汉升", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 4238, "processingTime": 10805}, {"chapterNumber": 398, "chapterTitle": "第397章 平平无奇萧容鱼，赚点小钱陈汉升", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 4339, "processingTime": 7054}, {"chapterNumber": 399, "chapterTitle": "第398章 谁的青春不懵懂？", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 2895, "processingTime": 2143}, {"chapterNumber": 400, "chapterTitle": "第399章 应对渣女的实际教学（上）", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 3782, "processingTime": 7853}], "model": "gemini-2.5-flash", "concurrency": 3}, "id": "mg0fvbgvdjmvwp1qmv8", "createdAt": "2025-09-26T06:06:22.927Z", "updatedAt": "2025-09-26T06:18:43.232Z", "result": "成功改写 300/301 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\我真没想重生啊"}, {"novelId": "0620431a73f462e4f3", "chapters": [189], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 1, "failedChapters": 0, "totalTokensUsed": 4016, "totalProcessingTime": 6058, "averageTimePerChapter": 6058, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 189, "chapterTitle": "第188章 当面教学的陈大师", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4016, "processingTime": 6042}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mg0grnvwydn5h48z75s", "createdAt": "2025-09-26T06:31:32.012Z", "updatedAt": "2025-09-26T06:31:38.073Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\我真没想重生啊"}, {"novelId": "0620431a73f462e4f3", "chapters": [189], "ruleId": "custom", "status": "failed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 0, "failedChapters": 1, "totalTokensUsed": 0, "totalProcessingTime": 32733, "averageTimePerChapter": 32733, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 189, "chapterTitle": "第188章 当面教学的陈大师", "success": false, "error": "内容被检测为违规内容", "apiKeyUsed": "chat", "processingTime": 32718}], "model": "gemini-2.5-flash", "concurrency": 4}, "id": "mg0gsqlpo3vbsq059u", "createdAt": "2025-09-26T06:32:22.189Z", "updatedAt": "2025-09-26T06:32:54.926Z", "result": "成功改写 0/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\我真没想重生啊"}, {"novelId": "0620431a73f462e4f3", "chapters": [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 21, "completedChapters": 21, "failedChapters": 0, "totalTokensUsed": 107930, "totalProcessingTime": 62866, "averageTimePerChapter": 2993.6190476190477, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 100, "chapterTitle": "第99章 关系就是生产力", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 5148, "processingTime": 7869}, {"chapterNumber": 101, "chapterTitle": "第100章 “水箭龟”王梓博", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 5058, "processingTime": 8033}, {"chapterNumber": 102, "chapterTitle": "第101章 不想当渣男", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4927, "processingTime": 7621}, {"chapterNumber": 103, "chapterTitle": "第102章 你挑着担，我牵着马", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 5448, "processingTime": 8775}, {"chapterNumber": 104, "chapterTitle": "第103章 心尖尖", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 5585, "processingTime": 9156}, {"chapterNumber": 105, "chapterTitle": "第104章 还真有个表妹", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 5574, "processingTime": 9466}, {"chapterNumber": 106, "chapterTitle": "第105章 四川的一晚（上）", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 5470, "processingTime": 10387}, {"chapterNumber": 107, "chapterTitle": "第106章 四川的一晚（下）", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 5402, "processingTime": 10138}, {"chapterNumber": 108, "chapterTitle": "第107章 12分的高数", "success": true, "error": "处理失败", "apiKeyUsed": "<PERSON><PERSON><PERSON>", "tokensUsed": 5049, "processingTime": 8425}, {"chapterNumber": 109, "chapterTitle": "第108章 生活系无赖", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 6078, "processingTime": 11189}, {"chapterNumber": 110, "chapterTitle": "第109章 聚会时的小意外", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 5136, "processingTime": 9769}, {"chapterNumber": 111, "chapterTitle": "第110章 2002版以身相许", "success": true, "error": "处理失败", "apiKeyUsed": "Generative Language Client", "tokensUsed": 4494, "processingTime": 7125}, {"chapterNumber": 112, "chapterTitle": "第111章 瑞雪兆丰年", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 5366, "processingTime": 9686}, {"chapterNumber": 113, "chapterTitle": "第112章 一个被放弃的大号", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 4339, "processingTime": 5898}, {"chapterNumber": 114, "chapterTitle": "第113章 修罗场（上）", "success": true, "error": "处理失败", "apiKeyUsed": "In The Novel", "tokensUsed": 5455, "processingTime": 10200}, {"chapterNumber": 115, "chapterTitle": "第114章 修罗场（下）", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 5283, "processingTime": 13928}, {"chapterNumber": 116, "chapterTitle": "第115章 新部长", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 5367, "processingTime": 8376}, {"chapterNumber": 117, "chapterTitle": "第116章 讹车", "success": true, "error": "处理失败", "apiKeyUsed": "chat", "tokensUsed": 4698, "processingTime": 6995}, {"chapterNumber": 118, "chapterTitle": "第117章 我想给你买辆车", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4606, "processingTime": 6792}, {"chapterNumber": 119, "chapterTitle": "第118章 珍惜简单", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4924, "processingTime": 7945}, {"chapterNumber": 120, "chapterTitle": "第119章 干就完事了", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4523, "processingTime": 6421}], "model": "gemini-flash-latest", "concurrency": 3}, "id": "mg0ijg1si9f5cz3xg9a", "createdAt": "2025-09-26T07:21:07.840Z", "updatedAt": "2025-09-26T07:22:10.711Z", "result": "成功改写 21/21 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\我真没想重生啊"}, {"novelId": "0620431a73f462e4f3", "chapters": [100], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 1, "failedChapters": 0, "totalTokensUsed": 4992, "totalProcessingTime": 6912, "averageTimePerChapter": 6912, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 100, "chapterTitle": "第99章 关系就是生产力", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 4992, "processingTime": 6901}], "model": "gemini-flash-latest", "concurrency": 4}, "id": "mg0k1ls4b26f0dmu46n", "createdAt": "2025-09-26T08:03:14.692Z", "updatedAt": "2025-09-26T08:03:21.609Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\我真没想重生啊"}, {"novelId": "0620431a73f462e4f3", "chapters": [100], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 1, "failedChapters": 0, "totalTokensUsed": 5220, "totalProcessingTime": 8059, "averageTimePerChapter": 8059, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 100, "chapterTitle": "第99章 关系就是生产力", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 5220, "processingTime": 8045}], "model": "gemini-flash-latest", "concurrency": 4}, "id": "mg0k44pyv1e71u8fa28", "createdAt": "2025-09-26T08:05:12.550Z", "updatedAt": "2025-09-26T08:05:20.613Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\我真没想重生啊"}, {"novelId": "0620431a73f462e4f3", "chapters": [100], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 1, "failedChapters": 0, "totalTokensUsed": 5046, "totalProcessingTime": 4599, "averageTimePerChapter": 4599, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 100, "chapterTitle": "第99章 关系就是生产力", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 5046, "processingTime": 4589}], "model": "gemini-flash-lite-latest", "concurrency": 4}, "id": "mg0k654q9dpwvolezet", "createdAt": "2025-09-26T08:06:46.394Z", "updatedAt": "2025-09-26T08:06:50.997Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\我真没想重生啊"}, {"novelId": "0620431a73f462e4f3", "chapters": [100], "ruleId": "custom", "status": "completed", "progress": 100, "details": {"totalChapters": 1, "completedChapters": 1, "failedChapters": 0, "totalTokensUsed": 5060, "totalProcessingTime": 5416, "averageTimePerChapter": 5416, "apiKeyStats": [], "chapterResults": [{"chapterNumber": 100, "chapterTitle": "第99章 关系就是生产力", "success": true, "error": "处理失败", "apiKeyUsed": "My First Project", "tokensUsed": 5060, "processingTime": 5399}], "model": "gemini-flash-lite-latest", "concurrency": 4}, "id": "mg0kgjwc6pt6eyu5ufv", "createdAt": "2025-09-26T08:14:52.092Z", "updatedAt": "2025-09-26T08:14:57.512Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\我真没想重生啊"}]