# 上下文分析API Key修复说明

## 问题描述
上下文分析API出现429错误（超过调用额度），需要让上下文分析功能只使用"My First Project"这个t1层级的项目key。

## 解决方案
修改了Gemini API调用逻辑，支持指定特定的API Key进行调用，同时保持其他功能的负载均衡不受影响。

## 修改内容

### 1. 修改 `src/lib/gemini.ts`

#### 1.1 扩展 `RewriteRequest` 接口
```typescript
export interface RewriteRequest {
  // ... 其他字段
  preferredApiKey?: string; // 新增：指定使用的API Key名称
}
```

#### 1.2 扩展 `ApiKeyManager` 类
```typescript
// 新增方法：获取指定名称的API Key
getKeyByName(keyName: string) {
  const key = this.keys.find(k => k.name === keyName);
  if (!key) {
    throw new Error(`API Key "${keyName}" 不存在`);
  }
  return key;
}
```

#### 1.3 修改 `rewriteText` 函数
```typescript
// 如果指定了preferredApiKey，使用指定的key，否则使用最佳可用的key
const apiKey = request.preferredApiKey 
  ? keyManager.getKeyByName(request.preferredApiKey)
  : keyManager.getBestAvailableKey();
```

### 2. 修改 `src/lib/context-analyzer.ts`

#### 2.1 小说整体分析
```typescript
const result = await rewriteText({
  originalText: analysisPrompt,
  rules: '请严格按照要求的JSON格式返回分析结果，不要添加任何其他内容。',
  chapterTitle: '小说整体分析',
  chapterNumber: 0,
  model: 'gemini-flash-lite-latest',
  preferredApiKey: 'My First Project' // 指定使用My First Project这个API key
});
```

#### 2.2 章节分析
```typescript
const result = await rewriteText({
  originalText: analysisPrompt,
  rules: '请严格按照要求的JSON格式返回分析结果，不要添加任何其他内容。',
  chapterTitle: `第${chapterNumber}章分析`,
  chapterNumber: chapterNumber,
  model: 'gemini-flash-lite-latest',
  preferredApiKey: 'My First Project' // 指定使用My First Project这个API key
});
```

## 效果

### 上下文分析功能
- 小说整体分析：只使用"My First Project" API Key
- 章节分析：只使用"My First Project" API Key
- 批量章节分析：只使用"My First Project" API Key

### 其他功能
- 文本改写：继续使用负载均衡，自动选择最佳可用的API Key
- 其他API调用：不受影响，继续使用原有的负载均衡逻辑

## API Key信息
- **My First Project**: t1层级，权重为4，适合上下文分析的高频调用
- **其他Keys**: 继续用于文本改写等功能的负载均衡

## 使用方法

### 指定API Key调用
```typescript
const result = await rewriteText({
  originalText: '文本内容',
  rules: '改写规则',
  preferredApiKey: 'My First Project' // 指定使用特定的API Key
});
```

### 负载均衡调用（默认）
```typescript
const result = await rewriteText({
  originalText: '文本内容',
  rules: '改写规则'
  // 不指定preferredApiKey，自动使用负载均衡
});
```

## 注意事项
1. 如果指定的API Key不存在，会抛出错误
2. 如果指定的API Key在冷却中，仍然会使用该Key并等待冷却结束
3. 上下文分析功能现在完全依赖"My First Project"这个Key的可用性
4. 其他功能不受影响，继续使用多Key负载均衡
