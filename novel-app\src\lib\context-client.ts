// 客户端上下文工具函数
import type { NovelContext, ChapterContext } from './database';

// 检查是否在客户端环境
function isClientSide(): boolean {
  return typeof window !== 'undefined';
}

// 客户端获取小说上下文
export async function getNovelContextClient(novelId: string): Promise<NovelContext | null> {
  if (!isClientSide()) {
    throw new Error('This function can only be used on client side');
  }

  try {
    const response = await fetch(`/api/context/novel?novelId=${novelId}`);
    const result = await response.json();

    if (!result.success) {
      console.warn('获取小说上下文失败:', result.error);
      return null;
    }

    return result.data;
  } catch (error) {
    console.error('获取小说上下文失败:', error);
    return null;
  }
}

// 客户端获取章节上下文
export async function getChapterContextClient(novelId: string, chapterNumber: number): Promise<ChapterContext | null> {
  if (!isClientSide()) {
    throw new Error('This function can only be used on client side');
  }

  try {
    const response = await fetch(`/api/context/chapter?novelId=${novelId}&chapterNumber=${chapterNumber}`);
    const result = await response.json();

    if (!result.success) {
      console.warn('获取章节上下文失败:', result.error);
      return null;
    }

    return result.data;
  } catch (error) {
    console.error('获取章节上下文失败:', error);
    return null;
  }
}

// 客户端获取章节上下文窗口
export async function getChapterContextWindowClient(
  novelId: string,
  chapterNumber: number,
  windowSize: number = 2
): Promise<ChapterContext[]> {
  if (!isClientSide()) {
    throw new Error('This function can only be used on client side');
  }

  try {
    const response = await fetch(`/api/context/window?novelId=${novelId}&chapterNumber=${chapterNumber}&windowSize=${windowSize}`);
    const result = await response.json();

    if (!result.success) {
      console.warn('获取章节上下文窗口失败:', result.error);
      return [];
    }

    return result.data.contexts;
  } catch (error) {
    console.error('获取章节上下文窗口失败:', error);
    return [];
  }
}

// 客户端分析小说上下文
export async function analyzeNovelClient(novelId: string, analyzeChapters: boolean = false): Promise<{
  novelContext: NovelContext;
  chapterContexts?: ChapterContext[];
}> {
  if (!isClientSide()) {
    throw new Error('This function can only be used on client side');
  }

  const response = await fetch('/api/context/analyze', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      novelId: novelId,
      analyzeChapters: analyzeChapters
    })
  });

  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || '分析失败');
  }

  return {
    novelContext: result.data.novelContext,
    chapterContexts: result.data.chapterContexts
  };
}

// 客户端带上下文的重写函数
export async function rewriteTextWithContextClient(
  novelId: string,
  chapterNumbers: number[],
  rules: string,
  model?: string,
  onProgress?: (progress: number, currentChapter: number) => void
): Promise<Array<{ success: boolean; content: string; error?: string }>> {
  if (!isClientSide()) {
    throw new Error('This function can only be used on client side');
  }

  const response = await fetch('/api/rewrite', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      novelId: novelId,
      chapterNumbers: chapterNumbers,
      rules: rules,
      model: model || 'gemini-flash-lite-latest'
    })
  });

  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || '重写失败');
  }

  return result.data.results;
}

// 通用的上下文获取函数（自动选择服务端或客户端）
export async function getNovelContext(novelId: string): Promise<NovelContext | null> {
  if (isClientSide()) {
    return getNovelContextClient(novelId);
  } else {
    // 服务端环境，动态导入服务端函数
    const { getNovelContextServer } = await import('./context-utils');
    return getNovelContextServer(novelId);
  }
}

// 通用的章节上下文获取函数（自动选择服务端或客户端）
export async function getChapterContext(novelId: string, chapterNumber: number): Promise<ChapterContext | null> {
  if (isClientSide()) {
    return getChapterContextClient(novelId, chapterNumber);
  } else {
    // 服务端环境，动态导入服务端函数
    const { getChapterContextServer } = await import('./context-utils');
    return getChapterContextServer(novelId, chapterNumber);
  }
}

// 通用的章节上下文窗口获取函数（自动选择服务端或客户端）
export async function getChapterContextWindow(
  novelId: string,
  chapterNumber: number,
  windowSize: number = 2
): Promise<ChapterContext[]> {
  if (isClientSide()) {
    return getChapterContextWindowClient(novelId, chapterNumber, windowSize);
  } else {
    // 服务端环境，动态导入服务端函数
    const { getChapterContextWindowServer } = await import('./context-utils');
    return getChapterContextWindowServer(novelId, chapterNumber, windowSize);
  }
}
