'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import NovelSelector from '@/components/NovelSelector';
import ChapterSelector from '@/components/ChapterSelector';
import RuleEditor from '@/components/RuleEditor';
import CharacterManager from '@/components/CharacterManager';
import RewriteProgress from '@/components/RewriteProgress';
import TaskManager from '@/components/TaskManager';
import ModelConfigSelector from '@/components/ModelConfigSelector';
import ChapterDiagnostics from '@/components/ChapterDiagnostics';
import Toast from '@/components/Toast';
import { Novel, Chapter } from '@/lib/database';
import { HelpCircle } from 'lucide-react';

interface Character {
  id: string;
  novelId: string;
  name: string;
  role: string;
  description: string;
  personality?: string;
  appearance?: string;
  relationships?: string;
}

interface ToastState {
  show: boolean;
  message: string;
  type: 'success' | 'error' | 'info';
}

export default function Home() {
  const [selectedNovel, setSelectedNovel] = useState<Novel | null>(null);
  const [selectedChapters, setSelectedChapters] = useState<string>('');
  const [rewriteRules, setRewriteRules] = useState<string>('');
  const [characters, setCharacters] = useState<Character[]>([]);
  const [isRewriting, setIsRewriting] = useState(false);
  const [currentJobId, setCurrentJobId] = useState<string | null>(null);
  const [showTaskManager, setShowTaskManager] = useState(false);
  const [showDiagnostics, setShowDiagnostics] = useState(false);
  const [chapters, setChapters] = useState<Chapter[]>([]);
  const [selectedModel, setSelectedModel] = useState<string>('gemini-flash-latest');
  const [selectedConcurrency, setSelectedConcurrency] = useState<number>(4);
  const [toast, setToast] = useState<ToastState>({ show: false, message: '', type: 'info' });

  const showToast = (message: string, type: 'success' | 'error' | 'info') => {
    setToast({ show: true, message, type });
  };

  const hideToast = () => {
    setToast({ show: false, message: '', type: 'info' });
  };

  // 加载章节数据
  const loadChapters = async (novelId: string) => {
    try {
      const response = await fetch(`/api/chapters?novelId=${encodeURIComponent(novelId)}`);
      const result = await response.json();

      if (result.success) {
        setChapters(result.data);
      } else {
        console.error('加载章节失败:', result.error);
        setChapters([]);
      }
    } catch (error) {
      console.error('加载章节失败:', error);
      setChapters([]);
    }
  };

  // 当选中的小说改变时，加载章节数据
  useEffect(() => {
    if (selectedNovel) {
      loadChapters(selectedNovel.id);
    } else {
      setChapters([]);
    }
  }, [selectedNovel]);

  const handleSaveToPreset = async (rules: string) => {
    const name = prompt('请输入预设名称:');
    if (!name) return;

    const description = prompt('请输入预设描述 (可选):') || '';

    try {
      const response = await fetch('/api/presets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          description,
          rules,
        }),
      });

      const result = await response.json();

      if (result.success) {
        showToast('规则已保存到预设', 'success');
      } else {
        showToast(`保存失败: ${result.error}`, 'error');
      }
    } catch (error) {
      console.error('保存预设失败:', error);
      showToast('保存预设失败', 'error');
    }
  };

  const handleStartRewrite = async () => {
    if (!selectedNovel || !selectedChapters || !rewriteRules) {
      showToast('请完整填写所有信息', 'error');
      return;
    }

    setIsRewriting(true);

    try {
      // 构建包含人物信息的改写规则
      let enhancedRules = rewriteRules;
      if (characters.length > 0) {
        const characterInfo = characters.map(char =>
          `${char.name}(${char.role}${char.description ? ': ' + char.description : ''})`
        ).join('、');
        enhancedRules = `人物设定：${characterInfo}\n\n${rewriteRules}`;
      }

      const response = await fetch('/api/rewrite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          novelId: selectedNovel.id,
          chapterRange: selectedChapters,
          rules: enhancedRules,
          model: selectedModel,
          concurrency: selectedConcurrency,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result && result.success) {
        setCurrentJobId(result.data.jobId);
        showToast('改写任务已开始', 'info');
      } else {
        showToast(`改写失败: ${result?.error || '未知错误'}`, 'error');
        setIsRewriting(false);
      }
    } catch (error) {
      console.error('改写请求失败:', error);
      showToast('改写请求失败，请检查网络连接', 'error');
      setIsRewriting(false);
    }
  };

  const handleRewriteComplete = () => {
    setIsRewriting(false);
    setCurrentJobId(null);
    showToast('改写完成！', 'success');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-4">
        {/* 头部 */}
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold text-gray-800">
            小说改写工具
          </h1>
          <div className="flex items-center space-x-4">
            {/* 任务管理按钮 */}
            <button
              onClick={() => setShowTaskManager(!showTaskManager)}
              className="flex items-center px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <svg className="mr-1" width={18} height={18} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                <path d="M9 9h6v6H9z"/>
              </svg>
              任务管理
            </button>
            {/* 开始改写按钮 */}
            <button
              onClick={handleStartRewrite}
              disabled={isRewriting || !selectedNovel || !selectedChapters || !rewriteRules}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-6 rounded-lg transition-colors"
              title={
                !selectedNovel ? '请先选择小说' :
                !selectedChapters ? '请选择章节范围' :
                !rewriteRules ? '请输入改写规则' :
                '开始改写任务'
              }
            >
              {isRewriting ? '改写中...' : '开始改写'}
            </button>
            <Link
              href="/context"
              className="flex items-center px-3 py-2 text-purple-600 hover:text-purple-800 hover:bg-purple-50 rounded-lg transition-colors"
            >
              <svg className="mr-1" width={18} height={18} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M9 12l2 2 4-4"/>
                <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"/>
                <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"/>
                <path d="M12 3v6m0 6v6"/>
              </svg>
              上下文管理
            </Link>
            <Link
              href="/merge"
              className="flex items-center px-3 py-2 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-lg transition-colors"
            >
              <svg className="mr-1" width={18} height={18} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                <polyline points="7,10 12,15 17,10"/>
                <line x1="12" y1="15" x2="12" y2="3"/>
              </svg>
              合并章节
            </Link>
            <button
              onClick={() => setShowDiagnostics(!showDiagnostics)}
              className="flex items-center px-3 py-2 text-orange-600 hover:text-orange-800 hover:bg-orange-50 rounded-lg transition-colors"
            >
              <svg className="mr-1" width={18} height={18} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M9 12l2 2 4-4"/>
                <circle cx="12" cy="12" r="10"/>
              </svg>
              章节诊断
            </button>
            <Link
              href="/help"
              className="flex items-center px-3 py-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors"
            >
              <HelpCircle className="mr-1" size={18} />
              帮助
            </Link>
          </div>
        </div>

        {/* 任务管理器 */}
        {showTaskManager && (
          <div className="mb-6">
            <TaskManager
              currentJobId={currentJobId || undefined}
              onJobComplete={handleRewriteComplete}
            />
          </div>
        )}

        {/* 进度显示 */}
        {isRewriting && currentJobId && !showTaskManager && (
          <div className="mb-4">
            <RewriteProgress
              jobId={currentJobId}
              onComplete={handleRewriteComplete}
            />
          </div>
        )}

        {/* 章节诊断 */}
        {showDiagnostics && !showTaskManager && (
          <div className="mb-6">
            <ChapterDiagnostics
              novel={selectedNovel}
              chapters={chapters}
            />
          </div>
        )}

        {!showTaskManager && !showDiagnostics && (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
          {/* 左侧：改写规则 */}
          <div className="lg:col-span-1">
            <RuleEditor
              rules={rewriteRules}
              onRulesChange={setRewriteRules}
              onSaveToPreset={handleSaveToPreset}
              disabled={isRewriting}
            />
          </div>

          {/* 中间左：小说选择和人物管理 */}
          <div className="lg:col-span-1 space-y-4">
            <NovelSelector
              selectedNovel={selectedNovel}
              onNovelSelect={setSelectedNovel}
              disabled={isRewriting}
            />
            <CharacterManager
              novelId={selectedNovel?.id}
              characters={characters}
              onCharactersChange={setCharacters}
              disabled={isRewriting}
            />
          </div>

          {/* 中间右：章节选择 */}
          <div className="lg:col-span-1">
            <ChapterSelector
              novel={selectedNovel}
              selectedChapters={selectedChapters}
              onChaptersChange={setSelectedChapters}
              disabled={isRewriting}
            />
          </div>

          {/* 右侧：模型配置 */}
          <div className="lg:col-span-1">
            <ModelConfigSelector
              selectedModel={selectedModel}
              selectedConcurrency={selectedConcurrency}
              onModelChange={setSelectedModel}
              onConcurrencyChange={setSelectedConcurrency}
              disabled={isRewriting}
            />
          </div>
        </div>
        )}
      </div>

      {/* Toast 通知 */}
      {toast.show && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={hideToast}
        />
      )}
    </div>
  );
}
