'use client';

import { useState } from 'react';
import { Setting<PERSON>, Cpu, Zap } from 'lucide-react';

interface ModelConfigSelectorProps {
  selectedModel: string;
  selectedConcurrency: number;
  onModelChange: (model: string) => void;
  onConcurrencyChange: (concurrency: number) => void;
  disabled?: boolean;
}

const AVAILABLE_MODELS = [
  {
    id: 'gemini-flash-lite-latest',
    name: 'Gemini Flash Lite Latest',
    description: '快速、轻量级模型，适合大批量处理',
    speed: 'fast',
    quality: 'good'
  },
  {
    id: 'gemini-flash-latest',
    name: 'Gemini Flash Latest',
    description: '平衡速度和质量的标准模型',
    speed: 'medium',
    quality: 'excellent'
  },
  {
    id: 'gemini-2.5-pro',
    name: 'Gemini 2.5 Pro',
    description: '高质量模型，处理复杂内容',
    speed: 'slow',
    quality: 'premium'
  }
];

const CONCURRENCY_OPTIONS = [
  { value: 1, label: '1 (最安全)', description: '单线程处理，最稳定' },
  { value: 2, label: '2 (保守)', description: '低并发，适合API限制严格的情况' },
  { value: 3, label: '3 (推荐)', description: '默认设置，平衡速度和稳定性' },
  { value: 4, label: '4 (积极)', description: '较高并发，需要充足的API配额' },
  { value: 5, label: '5 (激进)', description: '高并发，适合API配额充足的情况' },
  { value: 6, label: '6 (极限)', description: '最高并发，可能触发API限制' }
];

export default function ModelConfigSelector({
  selectedModel,
  selectedConcurrency,
  onModelChange,
  onConcurrencyChange,
  disabled = false
}: ModelConfigSelectorProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const selectedModelInfo = AVAILABLE_MODELS.find(m => m.id === selectedModel);
  const selectedConcurrencyInfo = CONCURRENCY_OPTIONS.find(c => c.value === selectedConcurrency);

  const getSpeedColor = (speed: string) => {
    switch (speed) {
      case 'fast': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'slow': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getQualityColor = (quality: string) => {
    switch (quality) {
      case 'good': return 'text-blue-600 bg-blue-100';
      case 'excellent': return 'text-purple-600 bg-purple-100';
      case 'premium': return 'text-indigo-600 bg-indigo-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <div className="flex items-center justify-between mb-3">
        <h2 className="text-lg font-semibold text-gray-800 flex items-center">
          <Settings className="mr-2" size={18} />
          模型配置
        </h2>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          disabled={disabled}
          className="p-1 text-gray-600 hover:text-gray-800 disabled:opacity-50"
          title={isExpanded ? "收起配置" : "展开配置"}
        >
          <Zap className={`transition-transform ${isExpanded ? 'rotate-180' : ''}`} size={16} />
        </button>
      </div>

      {/* 简化显示 */}
      {!isExpanded && (
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">当前模型:</span>
            <span className="font-medium">{selectedModelInfo?.name || selectedModel}</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">并发数:</span>
            <span className="font-medium">{selectedConcurrency}</span>
          </div>
        </div>
      )}

      {/* 详细配置 */}
      {isExpanded && (
        <div className="space-y-4">
          {/* 模型选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Cpu className="inline mr-1" size={14} />
              AI 模型
            </label>
            <div className="space-y-2">
              {AVAILABLE_MODELS.map((model) => (
                <label key={model.id} className="flex items-start space-x-3 cursor-pointer">
                  <input
                    type="radio"
                    name="model"
                    value={model.id}
                    checked={selectedModel === model.id}
                    onChange={(e) => onModelChange(e.target.value)}
                    disabled={disabled}
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-800">{model.name}</span>
                      <span className={`px-2 py-1 rounded-full text-xs ${getSpeedColor(model.speed)}`}>
                        {model.speed === 'fast' ? '快速' : model.speed === 'medium' ? '中等' : '慢速'}
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs ${getQualityColor(model.quality)}`}>
                        {model.quality === 'good' ? '良好' : model.quality === 'excellent' ? '优秀' : '顶级'}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{model.description}</p>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* 并发数选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Zap className="inline mr-1" size={14} />
              并发数量
            </label>
            <div className="space-y-2">
              {CONCURRENCY_OPTIONS.map((option) => (
                <label key={option.value} className="flex items-start space-x-3 cursor-pointer">
                  <input
                    type="radio"
                    name="concurrency"
                    value={option.value}
                    checked={selectedConcurrency === option.value}
                    onChange={(e) => onConcurrencyChange(parseInt(e.target.value))}
                    disabled={disabled}
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-800">{option.label}</span>
                      {option.value === 3 && (
                        <span className="px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-600">推荐</span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{option.description}</p>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* 配置提示 */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div className="flex items-start">
              <div className="text-yellow-600 mr-2">⚠️</div>
              <div className="text-sm text-yellow-800">
                <p className="font-medium mb-1">配置建议:</p>
                <ul className="space-y-1 text-xs">
                  <li>• 首次使用建议选择 &quot;Gemini 2.5 Flash Lite&quot; + 并发数 3</li>
                  <li>• 如果遇到 429 错误，请降低并发数</li>
                  <li>• API 配额充足时可以提高并发数以加快处理速度</li>
                  <li>• 高质量要求的内容建议使用 &quot;Gemini 1.5 Pro&quot;</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
