import { NextRequest, NextResponse } from 'next/server';
import { chapterDb, jobDb, novelDb } from '@/lib/database';
import { rewriteTextWithContextServer } from '@/lib/context-utils';
import { fileManager } from '@/lib/file-manager';
import path from 'path';

// POST - 重试失败的章节
export async function POST(request: NextRequest) {
  try {
    const { jobId, chapterNumbers, rules, model } = await request.json();

    if (!jobId || !chapterNumbers || !Array.isArray(chapterNumbers) || !rules) {
      return NextResponse.json(
        { success: false, error: '参数不完整' },
        { status: 400 }
      );
    }

    // 获取任务信息
    const job = jobDb.getById(jobId);
    if (!job) {
      return NextResponse.json(
        { success: false, error: '任务不存在' },
        { status: 404 }
      );
    }

    // 获取小说信息
    const novel = novelDb.getById(job.novelId);
    if (!novel) {
      return NextResponse.json(
        { success: false, error: '小说不存在' },
        { status: 404 }
      );
    }

    // 获取要重试的章节
    const allChapters = chapterDb.getByNovelId(job.novelId);
    const chaptersToRetry = allChapters.filter(chapter =>
      chapterNumbers.includes(chapter.chapterNumber)
    );

    if (chaptersToRetry.length === 0) {
      return NextResponse.json(
        { success: false, error: '没有找到指定的章节' },
        { status: 404 }
      );
    }

    // 更新任务状态
    jobDb.update(jobId, {
      status: 'processing',
      details: {
        ...job.details,
        retryInProgress: true,
        retryChapters: chapterNumbers,
      }
    });

    // 异步执行重试
    executeRetryJob(jobId, chaptersToRetry, rules, novel.title, model || 'gemini-flash-lite-latest');

    return NextResponse.json({
      success: true,
      data: {
        jobId,
        retryChaptersCount: chaptersToRetry.length,
        message: '重试任务已创建，正在处理中...',
      },
    });

  } catch (error) {
    console.error('创建重试任务失败:', error);
    return NextResponse.json(
      { success: false, error: '创建重试任务失败' },
      { status: 500 }
    );
  }
}

// 异步执行重试任务
async function executeRetryJob(
  jobId: string,
  chapters: Array<{
    id: string;
    novelId: string;
    chapterNumber: number;
    title: string;
    content: string;
    filename: string;
    createdAt: string;
  }>,
  rules: string,
  novelTitle: string,
  model: string = 'gemini-flash-lite-latest'
) {
  const startTime = Date.now();
  const outputDir = fileManager.getNovelRewrittenDir(novelTitle);
  let successCount = 0;
  const retryResults: Array<{
    chapterNumber: number;
    chapterTitle: string;
    success: boolean;
    error?: string;
    apiKeyUsed?: string;
    tokensUsed?: number;
    processingTime?: number;
  }> = [];

  try {
    // 串行处理重试章节，避免API限制
    for (const chapter of chapters) {
      console.log(`正在重试第 ${chapter.chapterNumber} 章: ${chapter.title}`);

      const chapterStartTime = Date.now();

      try {
        // 等待一段时间再处理下一个章节
        if (retryResults.length > 0) {
          await new Promise(resolve => setTimeout(resolve, 3000));
        }

        const result = await rewriteTextWithContextServer(
          chapter.novelId,
          chapter.chapterNumber,
          chapter.content,
          rules,
          chapter.title,
          model
        );

        const chapterProcessingTime = Date.now() - chapterStartTime;

        if (result.success) {
          // 保存成功重试的章节
          const filename = `chapter_${chapter.chapterNumber}_rewritten.txt`;
          const filePath = path.join(outputDir, filename);
          fileManager.writeFile(filePath, result.rewrittenText);
          successCount++;
        }

        retryResults.push({
          chapterNumber: chapter.chapterNumber,
          chapterTitle: chapter.title,
          success: result.success,
          error: result.error,
          apiKeyUsed: result.apiKeyUsed,
          tokensUsed: result.tokensUsed,
          processingTime: chapterProcessingTime,
        });

        console.log(`第 ${chapter.chapterNumber} 章重试${result.success ? '成功' : '失败'}: ${result.error || '完成'}`);

      } catch (error) {
        const chapterProcessingTime = Date.now() - chapterStartTime;
        retryResults.push({
          chapterNumber: chapter.chapterNumber,
          chapterTitle: chapter.title,
          success: false,
          error: `重试异常: ${error instanceof Error ? error.message : '未知错误'}`,
          processingTime: chapterProcessingTime,
        });
        console.error(`第 ${chapter.chapterNumber} 章重试异常:`, error);
      }
    }

    // 更新任务状态
    const currentJob = jobDb.getById(jobId);
    if (currentJob?.details) {
      const updatedChapterResults = [...(currentJob.details.chapterResults || [])];

      // 更新重试章节的结果
      retryResults.forEach(retryResult => {
        const existingIndex = updatedChapterResults.findIndex(
          r => r && r.chapterNumber === retryResult.chapterNumber
        );

        if (existingIndex >= 0) {
          updatedChapterResults[existingIndex] = {
            ...updatedChapterResults[existingIndex],
            ...retryResult,
            completedAt: new Date().toISOString(),
            isRetried: true,
          };
        }
      });

      const totalProcessingTime = Date.now() - startTime;
      const newCompletedCount = updatedChapterResults.filter(r => r && r.success).length;
      const newFailedCount = updatedChapterResults.filter(r => r && !r.success).length;

      jobDb.update(jobId, {
        status: 'completed',
        progress: 100,
        result: `重试完成: ${successCount}/${chapters.length} 章节成功，总计 ${newCompletedCount}/${currentJob.details.totalChapters} 章节完成`,
        details: {
          ...currentJob.details,
          completedChapters: newCompletedCount,
          failedChapters: newFailedCount,
          chapterResults: updatedChapterResults,
          retryInProgress: false,
          retryResults,
          lastRetryTime: new Date().toISOString(),
          retryProcessingTime: totalProcessingTime,
        }
      });
    }

    // 保存重试结果摘要
    const retrySummaryPath = path.join(outputDir, `retry_summary_${Date.now()}.json`);
    const retrySummaryData = JSON.stringify({
      jobId,
      novelTitle,
      retryChapters: chapters.map(c => ({ number: c.chapterNumber, title: c.title })),
      successCount,
      failedCount: chapters.length - successCount,
      totalProcessingTime: Date.now() - startTime,
      results: retryResults,
      completedAt: new Date().toISOString(),
      model,
    }, null, 2);
    fileManager.writeFile(retrySummaryPath, retrySummaryData);

  } catch (error) {
    console.error('执行重试任务失败:', error);
    jobDb.update(jobId, {
      status: 'failed',
      result: `重试失败: ${error instanceof Error ? error.message : '未知错误'}`,
      details: {
        ...jobDb.getById(jobId)?.details,
        retryInProgress: false,
        retryError: error instanceof Error ? error.message : '未知错误',
      }
    });
  }
}
